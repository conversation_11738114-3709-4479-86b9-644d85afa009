{"arrowParens": "always", "bracketSameLine": false, "bracketSpacing": true, "semi": true, "experimentalTernaries": false, "singleQuote": true, "jsxSingleQuote": false, "quoteProps": "as-needed", "trailingComma": "all", "singleAttributePerLine": false, "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "proseWrap": "preserve", "insertPragma": false, "printWidth": 80, "requirePragma": false, "tabWidth": 4, "useTabs": false, "embeddedLanguageFormatting": "auto", "plugins": ["@prettier/plugin-php", "@shufo/prettier-plugin-blade"], "overrides": [{"files": ["*.blade.php"], "options": {"parser": "blade", "printWidth": 120, "tabWidth": 4, "wrapAttributes": "auto", "wrapAttributesMinAttrs": 2, "sortTailwindcssClasses": true, "sortHtmlAttributes": "none", "noPhpSyntaxCheck": false, "indentInnerHtml": true, "extraLiners": "", "trailingCommaPHP": true}}]}