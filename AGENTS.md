## Project Details
- Language: PHP 8.4
- Framework: <PERSON><PERSON> 12
- Package Manager: Composer

## Setup
- To install dependencies: `composer install`
- To run tests: `composer test`

## Coding Standards
- PSR‑12, formatted via `vendor/bin/php-cs-fixer fix`.

## Database
- Uses SQLite. DSN: `sqlite::memory:`

## Context7 Integration
Context7 provides live, version-specific PHP/Laravel documentation via MCP.
Use MCP to retrieve documentation for Laravel classes and methods.

### Requirements
- Node.js ≥ 18
- An MCP-compatible client (<PERSON><PERSON><PERSON>, <PERSON><PERSON>rf, <PERSON>, VS Code, etc.)

### How to Install

#### <PERSON>ursor, <PERSON><PERSON>rf, <PERSON>, VS Code (Local MCP)
```jsonc
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"]
    }
  }
}
