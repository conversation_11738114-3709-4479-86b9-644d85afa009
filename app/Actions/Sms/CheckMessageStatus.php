<?php

declare(strict_types=1);

namespace App\Actions\Sms;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Throwable;

class CheckMessageStatus
{
    protected string $baseUrl;

    public function __construct(protected string $authToken)
    {
        $this->baseUrl = config('sms.base_url', 'http://sms.test/api');
    }

    public function __invoke(string $messageId): array
    {
        try {
            $response = Http::withToken($this->authToken)
                ->get("{$this->baseUrl}/sms/messages/".$messageId);

            Log::info('SMS status check response', ['response' => $response->json()]);

            return [
                'success' => $response->successful(),
                'response' => $response->json() ?? $response->body(),
            ];
        } catch (Throwable $e) {
            Log::error('Failed to check SMS status', ['error' => $e->getMessage()]);

            return [
                'success' => false,
                'response' => $e->getMessage(),
            ];
        }
    }
}
