<?php

declare(strict_types=1);

namespace App\Actions\Sms;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Throwable;

class SendTemplateSms
{
    protected string $baseUrl;

    public function __construct(
        protected string $authToken,
        protected ?string $defaultSenderId = null,
        protected ?string $defaultPaymentType = null,
    ) {
        $this->baseUrl = config('sms.base_url', 'http://sms.test/api');
    }

    /**
     * Send an SMS using a template.
     *
     * @param  array  $payload  [
     *                          'template_id' => string,
     *                          'receiver' => string,
     *                          'sender' => string|null,
     *                          'payment_type' => string|null,
     *                          'params' => array|null,
     *                          ]
     * @return array{success: bool, response: mixed}
     */
    public function __invoke(array $payload): array
    {
        try {
            $data = [
                'template_id' => $payload['template_id'],
                'receiver' => $payload['receiver'],
                'sender' => $payload['sender'] ?? $this->defaultSenderId,
                'payment_type' => $payload['payment_type'] ?? $this->defaultPaymentType,
                'params' => array_values($payload['params'] ?? []),
            ];

            $response = Http::withToken($this->authToken)
                ->withHeader('Accept', 'application/json')
                ->withHeader('Content-Type', 'application/json')
                ->post("{$this->baseUrl}/sms/messages/template", $data);

            return [
                'success' => $response->successful(),
                'response' => $this->parseResponse($response),
            ];
        } catch (Exception $e) {
            Log::error('SMS sending failed', ['exception' => $e]);

            return [
                'success' => false,
                'response' => $e->getMessage(),
            ];
        }
    }

    protected function parseResponse($response): mixed
    {
        try {
            return $response->json() ?? $response->body();
        } catch (Throwable) {
            return $response->body();
        }
    }
}
