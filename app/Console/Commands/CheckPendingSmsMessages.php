<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\CheckSmsMessageStatusJob;
use App\Models\SmsMessage;
use Illuminate\Console\Command;

class CheckPendingSmsMessages extends Command
{
    protected $signature = 'check:pending-sms';

    protected $description = 'Check the delivery status of SMS messages that have not been delivered yet.';

    public function handle(): void
    {
        SmsMessage::where(function ($query) {
            $query->whereNull('delivery_status')
                ->orWhere('delivery_status', '')
                ->orWhere('delivery_status', 'pending');
        })
            ->whereNotNull('message_id')
            ->get()
            ->each(fn ($sms) => CheckSmsMessageStatusJob::dispatch($sms));

        $this->info('Dispatched delivery status checks for pending messages.');
    }
}
