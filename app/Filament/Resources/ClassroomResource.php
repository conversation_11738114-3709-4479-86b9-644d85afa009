<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\ClassroomResource\Pages\ClassroomReport;
use App\Filament\Resources\ClassroomResource\Pages\CreateClassroom;
use App\Filament\Resources\ClassroomResource\Pages\EditClassroom;
use App\Filament\Resources\ClassroomResource\Pages\ListClassrooms;
use App\Filament\Resources\ClassroomResource\Pages\ManageAttendance;
use App\Filament\Resources\ClassroomResource\Pages\ManageSchedules;
use App\Filament\Resources\ClassroomResource\Pages\ManageStudents;
use App\Models\Classroom;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use UnitEnum;

class ClassroomResource extends Resource
{
    protected static ?string $model = Classroom::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-building-library';

    protected static string|UnitEnum|null $navigationGroup = 'School Management';

    protected static ?int $navigationSort = 4;

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function getPluralModelLabel(): string
    {
        return __('Classrooms');
    }

    public static function getLabel(): string
    {
        return __('Classroom');
    }

    public static function getNavigationGroup(): ?string
    {
        return __(static::$navigationGroup);
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make(__('Classroom Information'))
                    ->schema([
                        TextInput::make('name')
                            ->label('Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255),

                        Textarea::make('description')
                            ->label('Description')
                            ->translateLabel()
                            ->rows(3),

                        Select::make('teacher_id')
                            ->label('Teacher')
                            ->translateLabel()
                            ->relationship('teacher', 'name')
                            ->preload()
                            ->searchable()
                            ->required(),

                        TextInput::make('capacity')
                            ->label('Capacity')
                            ->translateLabel()
                            ->numeric()
                            ->required()
                            ->minValue(1),

                        Toggle::make('is_active')
                            ->label('Active')
                            ->translateLabel()
                            ->default(true),
                    ])->columns(1),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('classroom_number')
                    ->label('Number')
                    ->translateLabel()
                    ->sortable(),
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('teacher.name')
                    ->label('Teacher')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('room_number')
                    ->label('Room Number')
                    ->translateLabel()
                    ->sortable(),

                TextColumn::make('capacity')
                    ->label('Capacity')
                    ->translateLabel()
                    ->formatStateUsing(function ($record) {
                        return $record->students()->count().' '.__('Student').' من '.$record->capacity;
                    })
                    ->sortable(),

                IconColumn::make('is_active')
                    ->label('Active')
                    ->translateLabel()
                    ->boolean(),

                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('teacher')
                    ->label('Teacher')
                    ->translateLabel()
                    ->relationship('teacher', 'name')
                    ->searchable(),

                TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->translateLabel(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditClassroom::class,
            ManageStudents::class,
            ManageAttendance::class,
            ManageSchedules::class,
            ClassroomReport::class,
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListClassrooms::route('/'),
            'create' => CreateClassroom::route('/create'),
            'edit' => EditClassroom::route('/{record}/edit'),
            'students' => ManageStudents::route('/{record}/students'),
            'attendance' => ManageAttendance::route('/{record}/attendance'),
            'schedules' => ManageSchedules::route('/{record}/schedules'),
            'report' => ClassroomReport::route('/{record}/report'),
        ];
    }
}
