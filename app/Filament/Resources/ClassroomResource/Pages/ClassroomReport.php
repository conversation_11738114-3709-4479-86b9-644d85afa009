<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassroomResource\Pages;

use App\Filament\Resources\ClassroomResource;
use App\Models\Classroom;
use BackedEnum;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;

class ClassroomReport extends ManageRelatedRecords
{
    public Classroom $classroom;

    protected static string $resource = ClassroomResource::class;

    protected static string $relationship = 'students';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-chart-bar';

    protected string $view = 'filament.resources.classroom-resource.pages.classroom-report';

    public static function getNavigationLabel(): string
    {
        return __('Classroom Report');
    }

    public function mount($record): void
    {
        parent::mount($record);
        $this->classroom = $this->getRelationship()->getParent();
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->withCount([
                'attendanceRecords as present_count' => fn ($q) => $q->where('type', 'Present'),
                'attendanceRecords as absent_count' => fn ($q) => $q->where('type', 'Absent'),
            ])
            )
            ->columns([
                TextColumn::make('name')
                    ->label(__('Student'))
                    ->searchable()
                    ->sortable(),
                TextColumn::make('present_count')
                    ->label(__('Days Present'))
                    ->sortable(),
                TextColumn::make('absent_count')
                    ->label(__('Days Absent'))
                    ->sortable(),
                TextColumn::make('id')
                    ->label(__('Attendance %'))
                    ->formatStateUsing(function ($record) {
                        $total = $record->present_count + $record->absent_count;

                        return $total > 0
                            ? number_format(($record->present_count / $total) * 100, 0).'%'
                            : '0%';
                    }),
            ])
            ->defaultSort('name');
    }

    public function getTitle(): string|Htmlable
    {
        return __('Classroom Report');
    }
}
