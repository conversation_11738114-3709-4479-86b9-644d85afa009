<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassroomResource\Pages;

use App\Enum\Weekday;
use App\Filament\Resources\ClassroomResource;
use BackedEnum;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ManageSchedules extends ManageRelatedRecords
{
    protected static string $resource = ClassroomResource::class;

    protected static string $relationship = 'schedules';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-calendar';

    public static function getNavigationLabel(): string
    {
        return __('Schedules');
    }

    public function getTitle(): string
    {
        return __('Schedules');
    }

    public function form(Schema $schema): Schema
    {
        $weekdayOptions = [];
        foreach (Weekday::cases() as $day) {
            $weekdayOptions[$day->value] = __($day->value);
        }

        return $schema
            ->components([
                Select::make('weekday')
                    ->label('Weekday')
                    ->translateLabel()
                    ->options($weekdayOptions)
                    ->required(),
                TimePicker::make('start_time')
                    ->label('Start Time')
                    ->translateLabel()
                    ->seconds(false)
                    ->required(),
                TimePicker::make('end_time')
                    ->label('End Time')
                    ->translateLabel()
                    ->seconds(false)
                    ->required(),
                Toggle::make('is_active')
                    ->label('Active')
                    ->translateLabel()
                    ->default(true),
            ])
            ->columns(2);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('weekday')
                    ->label('Weekday')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->sortable(),
                TextColumn::make('start_time')
                    ->label('Start Time')
                    ->translateLabel()
                    ->time(),
                TextColumn::make('end_time')
                    ->label('End Time')
                    ->translateLabel()
                    ->time(),
                IconColumn::make('is_active')
                    ->label('Active')
                    ->translateLabel()
                    ->boolean(),
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('Add Schedule')
                    ->translateLabel(),
            ])
            ->recordActions([
                EditAction::make()->slideOver(),
                DeleteAction::make(),
            ])
            ->defaultSort('weekday');
    }
}
