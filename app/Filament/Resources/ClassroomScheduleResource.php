<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enum\Weekday;
use App\Filament\Resources\ClassroomScheduleResource\Pages\CreateClassroomSchedule;
use App\Filament\Resources\ClassroomScheduleResource\Pages\EditClassroomSchedule;
use App\Filament\Resources\ClassroomScheduleResource\Pages\ListClassroomSchedules;
use App\Models\Classroom;
use App\Models\ClassroomSchedule;
use BackedEnum;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use UnitEnum;

class ClassroomScheduleResource extends Resource
{
    protected static ?string $model = ClassroomSchedule::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-calendar';

    protected static string|UnitEnum|null $navigationGroup = 'School Management';

    protected static ?int $navigationSort = 5;

    public static function getPluralModelLabel(): string
    {
        return __('Classroom Schedules');
    }

    public static function getLabel(): string
    {
        return __('Classroom Schedule');
    }

    public static function getNavigationGroup(): ?string
    {
        return __(static::$navigationGroup);
    }

    public static function form(Schema $schema): Schema
    {
        $weekdayOptions = [];
        foreach (Weekday::cases() as $day) {
            $weekdayOptions[$day->value] = __(ucfirst($day->value));
        }

        return $schema
            ->components([
                Select::make('classroom_id')
                    ->label('Classroom')
                    ->translateLabel()
                    ->options(Classroom::query()->pluck('name', 'id'))
                    ->required(),
                Select::make('weekday')
                    ->label('Weekday')
                    ->translateLabel()
                    ->options($weekdayOptions)
                    ->required(),
                TimePicker::make('start_time')
                    ->label('Start Time')
                    ->translateLabel()
                    ->seconds(false)
                    ->required(),
                TimePicker::make('end_time')
                    ->label('End Time')
                    ->translateLabel()
                    ->seconds(false)
                    ->required(),
                Toggle::make('is_active')
                    ->label('Active')
                    ->translateLabel()
                    ->default(true),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('classroom.name')
                    ->label('Classroom')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('weekday')
                    ->label('Weekday')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('start_time')
                    ->label('Start Time')
                    ->translateLabel()
                    ->time(),
                TextColumn::make('end_time')
                    ->label('End Time')
                    ->translateLabel()
                    ->time(),
                IconColumn::make('is_active')
                    ->label('Active')
                    ->translateLabel()
                    ->boolean(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->defaultSort('weekday');
    }

    public static function getPages(): array
    {
        return [
            'index' => ListClassroomSchedules::route('/'),
            'create' => CreateClassroomSchedule::route('/create'),
            'edit' => EditClassroomSchedule::route('/{record}/edit'),
        ];
    }
}
