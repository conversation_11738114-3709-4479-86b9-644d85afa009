<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassroomScheduleResource\Pages;

use App\Filament\Resources\ClassroomScheduleResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListClassroomSchedules extends ListRecords
{
    protected static string $resource = ClassroomScheduleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
