<?php

namespace App\Filament\Resources\ExamAspects;

use App\Filament\Resources\ExamAspects\Pages\CreateExamAspect;
use App\Filament\Resources\ExamAspects\Pages\EditExamAspect;
use App\Filament\Resources\ExamAspects\Pages\ListExamAspects;
use App\Filament\Resources\ExamAspects\Schemas\ExamAspectForm;
use App\Filament\Resources\ExamAspects\Tables\ExamAspectsTable;
use App\Models\ExamAspect;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class ExamAspectResource extends Resource
{
    protected static ?string $model = ExamAspect::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'ExamAspect';

    public static function form(Schema $schema): Schema
    {
        return ExamAspectForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ExamAspectsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListExamAspects::route('/'),
            'create' => CreateExamAspect::route('/create'),
            'edit' => EditExamAspect::route('/{record}/edit'),
        ];
    }
}
