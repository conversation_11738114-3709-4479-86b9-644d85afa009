<?php

namespace App\Filament\Resources\ExamSettingProfiles;

use App\Filament\Resources\ExamSettingProfiles\Pages\CreateExamSettingProfile;
use App\Filament\Resources\ExamSettingProfiles\Pages\EditExamSettingProfile;
use App\Filament\Resources\ExamSettingProfiles\Pages\ListExamSettingProfiles;
use App\Filament\Resources\ExamSettingProfiles\Schemas\ExamSettingProfileForm;
use App\Filament\Resources\ExamSettingProfiles\Tables\ExamSettingProfilesTable;
use App\Models\ExamSettingProfile;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class ExamSettingProfileResource extends Resource
{
    protected static ?string $model = ExamSettingProfile::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'ExamSettingProfile';

    public static function form(Schema $schema): Schema
    {
        return ExamSettingProfileForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ExamSettingProfilesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListExamSettingProfiles::route('/'),
            'create' => CreateExamSettingProfile::route('/create'),
            'edit' => EditExamSettingProfile::route('/{record}/edit'),
        ];
    }
}
