<?php

namespace App\Filament\Resources\ExamSettingProfiles\Pages;

use App\Filament\Resources\ExamSettingProfiles\ExamSettingProfileResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Str;

class CreateExamSettingProfile extends CreateRecord
{
    protected static string $resource = ExamSettingProfileResource::class;

    protected function beforeValidate(): void
    {
        // 1) Get current raw state (includes nested repeaters)
        $data = $this->form->getRawState() ?? [];

        // 2) Normalize aspects array
        $data['aspects'] = collect($data['aspects'] ?? [])
            ->map(function ($aspect) {
                // normalize label/key
                $label = trim((string) ($aspect['label'] ?? ''));
                $key = (string) ($aspect['key'] ?? '');

                // auto-slug key if empty or matches old slug of label
                if ($label !== '') {
                    $slugFromLabel = Str::slug($label, '_');
                    if (
                        $key === '' ||
                        $key ===
                            Str::slug((string) ($aspect['label'] ?? ''), '_')
                    ) {
                        $key = $slugFromLabel;
                    }
                }

                $aspect['label'] = $label;
                $aspect['key'] = Str::of($key)->lower()->snake()->value(); // enforce snake

                // coerce numeric fields
                $aspect['weight'] = isset($aspect['weight'])
                    ? (float) $aspect['weight']
                    : 0;
                $aspect['has_sub_aspects'] =
                    (bool) ($aspect['has_sub_aspects'] ?? false);
                $aspect['deduction_per_mistake'] = isset(
                    $aspect['deduction_per_mistake'],
                )
                    ? (float) $aspect['deduction_per_mistake']
                    : null;
                $aspect['fail_threshold'] = isset($aspect['fail_threshold'])
                    ? (int) $aspect['fail_threshold']
                    : 0;

                // 3) Normalize sub-aspects if grouped
                $subAs = collect($aspect['subAspects'] ?? [])
                    ->filter(
                        fn($row) => !empty($row['label']) ||
                            !empty($row['key']),
                    )
                    ->map(function ($row) {
                        $lbl = trim((string) ($row['label'] ?? ''));
                        $ky = (string) ($row['key'] ?? '');

                        if ($lbl !== '' && $ky === '') {
                            $ky = Str::slug($lbl, '_');
                        }

                        return [
                            'label' => $lbl,
                            'key' => Str::of($ky)->lower()->snake()->value(),
                            'deduction_per_mistake' => isset(
                                $row['deduction_per_mistake'],
                            )
                                ? (float) $row['deduction_per_mistake']
                                : 0,
                            'fail_threshold' => isset($row['fail_threshold'])
                                ? (int) $row['fail_threshold']
                                : 0,
                            // keep existing id/sort if present
                            'id' => $row['id'] ?? null,
                            'sort' => isset($row['sort'])
                                ? (int) $row['sort']
                                : 0,
                        ];
                    })
                    // dedupe sub keys within the same aspect
                    ->unique('key')
                    ->values()
                    ->all();

                $aspect['subAspects'] = $subAs;

                // if grouped, ensure fail_aspect_key is valid or null
                if (!empty($aspect['has_sub_aspects'])) {
                    $keys = collect($subAs)->pluck('key')->all();
                    $aspect['fail_aspect_key'] = in_array(
                        $aspect['fail_aspect_key'] ?? null,
                        $keys,
                        true,
                    )
                        ? $aspect['fail_aspect_key']
                        : null;
                } else {
                    // flat aspect: these grouped-only fields shouldn’t exist
                    $aspect['fail_aspect_key'] = null;
                    $aspect['subAspects'] = [];
                }

                return $aspect;
            })
            // dedupe aspect keys within the profile form
            ->unique('key')
            ->values()
            ->all();

        // 4) Write back sanitized state so validation runs on this data
        $this->form->fill($data);
    }
}
