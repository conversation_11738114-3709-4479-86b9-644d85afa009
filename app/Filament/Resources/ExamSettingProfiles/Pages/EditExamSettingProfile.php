<?php

namespace App\Filament\Resources\ExamSettingProfiles\Pages;

use App\Filament\Resources\ExamSettingProfiles\ExamSettingProfileResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Str;

class EditExamSettingProfile extends EditRecord
{
    protected static string $resource = ExamSettingProfileResource::class;

    protected function getHeaderActions(): array
    {
        return [DeleteAction::make()];
    }

    protected function beforeValidate(): void
    {
        // 1) Use state (has _key) instead of raw state
        $state = $this->form->getState() ?? [];

        $aspects = collect($state['aspects'] ?? [])
            ->map(function (array $aspect) {
                // Ensure each repeater item has a _key
                $aspect['_key'] = $aspect['_key'] ?? (string) Str::uuid();

                // Normalize label/key but KEEP _key and id
                $label = trim((string) ($aspect['label'] ?? ''));
                $key = (string) ($aspect['key'] ?? '');

                if ($label !== '') {
                    $slugFromLabel = Str::slug($label, '_');
                    // Only auto-set if empty or still the old slug
                    if (
                        $key === '' ||
                        $key ===
                            Str::slug((string) ($aspect['label'] ?? ''), '_')
                    ) {
                        $key = $slugFromLabel;
                    }
                }

                $aspect['label'] = $label;
                $aspect['key'] = Str::of($key)->lower()->snake()->value();

                // Coerce fields
                $aspect['weight'] = isset($aspect['weight'])
                    ? (float) $aspect['weight']
                    : 0;
                $aspect['has_sub_aspects'] =
                    (bool) ($aspect['has_sub_aspects'] ?? false);
                $aspect['deduction_per_mistake'] = isset(
                    $aspect['deduction_per_mistake'],
                )
                    ? (float) $aspect['deduction_per_mistake']
                    : null;
                $aspect['fail_threshold'] = isset($aspect['fail_threshold'])
                    ? (int) $aspect['fail_threshold']
                    : 0;

                // Sub-aspects: preserve _key, normalize, and dedupe by normalized key within the parent
                $subs = collect($aspect['subAspects'] ?? [])
                    ->map(function (array $row) {
                        $row['_key'] = $row['_key'] ?? (string) Str::uuid();

                        $lbl = trim((string) ($row['label'] ?? ''));
                        $ky = (string) ($row['key'] ?? '');
                        if ($lbl !== '' && $ky === '') {
                            $ky = Str::slug($lbl, '_');
                        }

                        $row['label'] = $lbl;
                        $row['key'] = Str::of($ky)->lower()->snake()->value();
                        $row['deduction_per_mistake'] = isset(
                            $row['deduction_per_mistake'],
                        )
                            ? (float) $row['deduction_per_mistake']
                            : 0;
                        $row['fail_threshold'] = isset($row['fail_threshold'])
                            ? (int) $row['fail_threshold']
                            : 0;
                        $row['sort'] = isset($row['sort'])
                            ? (int) $row['sort']
                            : 0;

                        return $row;
                    })
                    // Remove exact duplicates by normalized key, keep the first occurrence (preserving its _key)
                    ->unique(fn($row) => strtolower($row['key'] ?? ''))
                    ->values()
                    ->all();

                $aspect['subAspects'] = $subs;

                // Validate fail_aspect_key against current sub list
                if (!empty($aspect['has_sub_aspects'])) {
                    $keys = collect($subs)->pluck('key')->all();
                    $aspect['fail_aspect_key'] = in_array(
                        $aspect['fail_aspect_key'] ?? null,
                        $keys,
                        true,
                    )
                        ? $aspect['fail_aspect_key']
                        : null;
                } else {
                    $aspect['fail_aspect_key'] = null;
                    $aspect['subAspects'] = [];
                }

                return $aspect;
            })
            // Remove exact duplicates by normalized aspect key, keep first occurrence
            ->unique(fn($aspect) => strtolower($aspect['key'] ?? ''))
            ->values()
            ->all();

        // 2) Write back, preserving all _key values
        $state['aspects'] = $aspects;

        // 3) Refill WITHOUT nulling missing fields (prevents unintended drops)
        $this->form->fill($state, false, false);
    }
}
