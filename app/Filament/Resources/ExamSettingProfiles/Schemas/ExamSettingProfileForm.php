<?php

namespace App\Filament\Resources\ExamSettingProfiles\Schemas;

use Exception;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Tabs;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ExamSettingProfileForm
{
    /**
     * @throws Exception
     */
    public static function configure(Schema $schema): Schema
    {
        return $schema->components([
            Tabs::make('Tabs')
                ->tabs([
                    Tabs\Tab::make(__('Profile Information'))->schema([
                        TextInput::make('name')
                            ->label(__('Name'))
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Toggle::make('is_active')
                            ->label(__('Active'))
                            ->helperText(
                                __('Only one profile can be active at a time'),
                            )
                            ->columnSpanFull(),

                        Textarea::make('description')
                            ->label(__('Description'))
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                    Tabs\Tab::make(__('Exam Aspects'))->schema([
                        static::getAspectsRepeater(),
                    ]),
                ])
                ->columnSpanFull(),
        ]);
    }

    /**
     * @throws Exception
     */
    private static function getAspectsRepeater(): Repeater
    {
        return Repeater::make('aspects')
            ->label(__('Exam Aspects'))
            ->relationship()
            ->schema([
                TextInput::make('label')
                    ->label(__('Label'))
                    ->required()
                    ->placeholder(__('Enter aspect label'))
                    ->live(onBlur: true) // only trigger after leaving the field
                    ->afterStateUpdated(function ($state, callable $set) {
                        // Only set the key if it's empty to avoid overwriting user edits
                        if (!empty($state)) {
                            $set('key', Str::slug($state, '_')); // Use underscore as separator
                        }
                    })
                    ->columnSpan(2),

                TextInput::make('key')
                    ->label(__('Key'))
                    ->required()
                    ->placeholder(
                        __(
                            'Enter unique key (letters, numbers, dashes, underscores only)',
                        ),
                    )
                    ->columnSpan(2),

                TextInput::make('weight')
                    ->label(__('Weight'))
                    ->required()
                    ->numeric()
                    ->step(0.01)
                    ->minValue(0)
                    ->maxValue(100)
                    ->suffix('%')
                    ->live(true)
                    ->placeholder(__('Enter weight percentage'))
                    ->helperText(function (Get $get) {
                        $aspects = $get('../../aspects') ?? [];
                        $total = 0;
                        foreach ($aspects as $aspect) {
                            if (
                                isset($aspect['weight']) &&
                                is_numeric($aspect['weight'])
                            ) {
                                $total += (float) $aspect['weight'];
                            }
                        }
                        return __('Current total') .
                            ': ' .
                            number_format($total, 2) .
                            '%';
                    })
                    ->columnSpan(2),

                Toggle::make('has_sub_aspects')
                    ->label(__('Has Sub Aspects'))
                    ->live()
                    ->columnSpan(2),

                // Conditional fields for flat aspects (has_sub_aspects = false)
                TextInput::make('deduction_per_mistake')
                    ->label(__('Deduction Per Mistake'))
                    ->numeric()
                    ->step(0.01)
                    ->minValue(0)
                    ->placeholder(__('Enter deduction amount per mistake'))
                    ->visible(fn(Get $get): bool => !$get('has_sub_aspects'))
                    ->required(fn(Get $get): bool => !$get('has_sub_aspects'))
                    ->columnSpan(3),

                TextInput::make('fail_threshold')
                    ->label(__('Fail Threshold'))
                    ->numeric()
                    ->integer()
                    ->minValue(0)
                    ->default(0)
                    ->placeholder(
                        __('Enter maximum number of mistakes before failure'),
                    )
                    ->visible(fn(Get $get): bool => !$get('has_sub_aspects'))
                    ->columnSpan(3),

                // Conditional fields for grouped aspects (has_sub_aspects = true)
                Select::make('fail_aspect_key')
                    ->label(__('Fail Aspect Key'))
                    ->placeholder(__('Select which sub-aspect causes failure'))
                    ->options(function (Get $get) {
                        $subAspects = $get('subAspects') ?? [];
                        $options = [];
                        foreach ($subAspects as $subAspect) {
                            if (
                                isset($subAspect['key']) &&
                                isset($subAspect['label'])
                            ) {
                                $options[$subAspect['key']] =
                                    $subAspect['label'];
                            }
                        }
                        return $options;
                    })
                    ->visible(
                        fn(Get $get): bool => (bool) $get('has_sub_aspects'),
                    )
                    ->columnSpan(6),

                static::getSubAspectsRepeater(),
            ])
            ->addable(
                fn(Get $get): bool => collect($get('aspects'))->sum(
                    fn($aspect) => (float) ($aspect['weight'] ?? 0),
                ) < 100,
            )
            ->helperText(function (Get $get) {
                $total = collect($get('aspects'))->sum(
                    fn($aspect) => (float) ($aspect['weight'] ?? 0),
                );
                return __('Current total: :total%', ['total' => $total]);
            })
            ->collapsed()
            ->orderColumn('sort')
            ->collapsible()
            ->itemLabel(fn(array $state): ?string => $state['label'] ?? null)
            ->addActionLabel(__('Add Aspect'))
            ->columns(6);
    }

    /**
     * @throws Exception
     */
    private static function getSubAspectsRepeater(): Repeater
    {
        return Repeater::make('subAspects')
            ->relationship()
            ->schema([
                TextInput::make('label')
                    ->label(__('Label'))
                    ->required()
                    ->placeholder(__('Enter sub-aspect label'))
                    ->columnSpan(2)
                    ->live(onBlur: true) // only trigger after leaving the field
                    ->afterStateUpdated(function ($state, callable $set) {
                        // Only set the key if it's empty to avoid overwriting user edits
                        if (!empty($state)) {
                            $set('key', Str::slug($state, '_')); // Use underscore as separator
                        }
                    }),

                TextInput::make('key')
                    ->label(__('Key'))
                    ->required()
                    ->placeholder(__('Enter unique key within this aspect'))
                    ->columnSpan(2),

                TextInput::make('deduction_per_mistake')
                    ->label(__('Deduction Per Mistake'))
                    ->required()
                    ->numeric()
                    ->step(0.01)
                    ->minValue(0)
                    ->placeholder(__('Enter deduction amount per mistake'))
                    ->columnSpan(2),

                TextInput::make('fail_threshold')
                    ->label(__('Fail Threshold'))
                    ->numeric()
                    ->integer()
                    ->minValue(0)
                    ->default(0)
                    ->placeholder(
                        __('Enter maximum number of mistakes before failure'),
                    )
                    ->columnSpan(2),
            ])
            ->collapsed()
            ->orderColumn('sort')
            ->itemLabel(fn(array $state): ?string => $state['label'] ?? null)
            ->addActionLabel(__('Add Sub Aspect'))
            ->visible(fn(Get $get): bool => (bool) $get('has_sub_aspects'))
            ->columns(4)
            ->columnSpanFull();
    }
}
