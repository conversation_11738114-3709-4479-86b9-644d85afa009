<?php

namespace App\Filament\Resources\ExamSettingProfiles\Tables;

use App\Models\ExamSettingProfile;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;

class ExamSettingProfilesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),

                IconColumn::make('is_active')
                    ->label(__('Active'))
                    ->boolean()
                    ->sortable()
                    ->action(
                        Action::make('toggle_active')->action(function (
                            ExamSettingProfile $record,
                        ) {
                            $record->update([
                                'is_active' => !$record->is_active,
                            ]);

                            Notification::make()
                                ->title(__('Profile updated successfully'))
                                ->success()
                                ->send();
                        }),
                    ),

                TextColumn::make('aspects_count')
                    ->label(__('Aspects Count'))
                    ->counts('aspects')
                    ->badge(),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TernaryFilter::make('is_active')
                    ->label(__('Active'))
                    ->placeholder(__('All'))
                    ->trueLabel(__('Active'))
                    ->falseLabel(__('Inactive')),
            ])
            ->recordActions([
                EditAction::make(),
                /*Action::make('duplicate')
                    ->label(__('Duplicate'))
                    ->icon('heroicon-o-document-duplicate')
                    ->action(function (ExamSettingProfile $record) {
                        $newProfile = $record->replicate();
                        $newProfile->name = $record->name . ' ' . __('(Copy)');
                        $newProfile->is_active = false;
                        $newProfile->save();

                        // Duplicate aspects and sub-aspects
                        foreach ($record->aspects as $aspect) {
                            $newAspect = $aspect->replicate();
                            $newAspect->profile_id = $newProfile->id;
                            $newAspect->save();

                            // Duplicate sub-aspects
                            foreach ($aspect->subAspects as $subAspect) {
                                $newSubAspect = $subAspect->replicate();
                                $newSubAspect->aspect_id = $newAspect->id;
                                $newSubAspect->save();
                            }
                        }

                        Notification::make()
                            ->title(__('Profile duplicated successfully'))
                            ->success()
                            ->send();
                    }),*/
                DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading(__('Delete Profile'))
                    ->modalDescription(
                        __('Are you sure you want to delete this profile?'),
                    ),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading(__('Delete Selected Profiles'))
                        ->modalDescription(
                            __(
                                'Are you sure you want to delete the selected profiles?',
                            ),
                        ),
                ]),
            ]);
    }
}
