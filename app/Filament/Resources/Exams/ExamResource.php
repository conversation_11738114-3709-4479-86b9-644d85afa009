<?php

declare(strict_types=1);

namespace App\Filament\Resources\Exams;

use App\Filament\Resources\Exams\Pages\CreateExam;
use App\Filament\Resources\Exams\Pages\EditExam;
use App\Filament\Resources\Exams\Pages\ListExams;
use App\Filament\Resources\Exams\Pages\ManageResults;
use App\Filament\Resources\Exams\Pages\ManageStudents;
use App\Filament\Resources\Exams\Pages\ViewExam;
use App\Filament\Resources\Exams\Schemas\ExamForm;
use App\Filament\Resources\Exams\Tables\ExamsTable;
use App\Models\Exam;
use BackedEnum;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;

class ExamResource extends Resource
{
    protected static ?string $model = Exam::class;

    protected static string|null|BackedEnum $navigationIcon = 'heroicon-o-clipboard-document';

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function getNavigationLabel(): string
    {
        return __('Exams');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Exams');
    }

    public static function getLabel(): string
    {
        return __('Exam');
    }

    public static function form(Schema $schema): Schema
    {
        return ExamForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ExamsTable::configure($table);
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            //            EditExam::class,
            ViewExam::class,
            ManageStudents::class,
            ManageResults::class,
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListExams::route('/'),
            'create' => CreateExam::route('/create'),
            'view' => ViewExam::route('/{record}'),
            //            'edit' => EditExam::route('/{record}/edit'),
            'students' => ManageStudents::route('/{record}/students'),
            'results' => ManageResults::route('/{record}/results'),
        ];
    }

    public function getTitle(): string
    {
        return __('Exams');
    }
}
