<?php

declare(strict_types=1);

namespace App\Filament\Resources\Exams\Pages;

use App\Filament\Resources\Exams\ExamResource;
use App\Models\ExamStudentQuestion;
use App\Models\ExamSettingProfile;
use App\Models\Setting;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;

class ManageResults extends ManageRelatedRecords
{
    protected static string $resource = ExamResource::class;

    protected static string $relationship = 'studentQuestions';

    protected static string|BackedEnum|null $navigationIcon = Heroicon::BookOpen;

    public static function getNavigationLabel(): string
    {
        return __('Manage Results');
    }

    public function getTitle(): string
    {
        return __('Manage Results');
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(
                fn ($query) => $query->with(['student', 'question', 'ratings']),
            )
            ->groups([Group::make('student.name')->label(__('Student'))])
            ->defaultGroup('student.name')
            ->columns($this->getDynamicColumns())
            ->recordActions([
                Action::make('editResult')
                    ->label('') // Remove label for icon-only
                    ->tooltip(__('Edit Result')) // Optional hover tooltip
                    ->icon('heroicon-o-pencil-square')
                    ->iconButton()
                    ->color('primary')
                    ->schema(function (ExamStudentQuestion $record) {
                        return $this->getDynamicFormFields($record);
                    })
                    ->action(function (
                        array $data,
                        ExamStudentQuestion $record,
                    ) {
                        foreach ($data as $aspect => $mistakes) {
                            $record
                                ->ratings()
                                ->updateOrCreate(
                                    ['aspect' => $aspect],
                                    ['mistakes' => $mistakes],
                                );
                        }
                    }),
                Action::make('resetQuestion')
                    ->label('')
                    ->tooltip(__('Reset Question'))
                    ->icon('heroicon-o-trash')
                    ->iconButton()
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading(__('Reset this Question?'))
                    ->modalDescription(
                        __('This will delete all ratings for this question. Are you sure?'),
                    )
                    ->action(function (ExamStudentQuestion $record) {
                        $record->ratings()->delete();
                    }),
            ]);
    }

    protected function getDynamicColumns(): array
    {
        $columns = [
            TextColumn::make('question_label')
                ->label('Question')
                ->translateLabel()
                ->state(function (ExamStudentQuestion $record) {
                    $question = $record->question;
                    return $question->title;
                }),
        ];

        // Get the exam setting profile
        $exam = $this->getRecord();
        $profile = $exam->examSettingProfile ?? ExamSettingProfile::getActive();

        if ($profile) {
            $flatAspects = $profile->getAllAspectsFlat();

            foreach ($flatAspects as $aspectKey => $config) {
                $columns[] = TextColumn::make($aspectKey . '_score')
                    ->label($config['label'])
                    ->state(function (ExamStudentQuestion $record) use ($aspectKey) {
                        $rating = $record->ratings->firstWhere('aspect', $aspectKey);
                        return $rating ? number_format($rating->score, 2) : '-';
                    })
                    ->sortable(false);
            }
        }

        // Add total score column
        $columns[] = TextColumn::make('total_score')
            ->label(__('Total Score'))
            ->state(function (ExamStudentQuestion $record) {
                $total = $record->ratings->sum('score');
                return number_format($total, 2);
            })
            ->sortable(false);

        return $columns;
    }

    protected function getDynamicFormFields(ExamStudentQuestion $record): array
    {
        $ratings = $record->ratings->keyBy('aspect');

        // Get the exam setting profile
        $exam = $this->getRecord();
        $profile = $exam->examSettingProfile ?? ExamSettingProfile::getActive();

        if (!$profile) {
            return [];
        }

        $flatAspects = $profile->getAllAspectsFlat();
        $fields = [];

        foreach ($flatAspects as $aspectKey => $config) {
            $weight = (float) $config['weight'];
            $deduction = (float) $config['deduction_per_mistake'];
            $failThreshold = (int) $config['fail_threshold'];

            $maxMistakesText = __('N/A');
            $maxMistakesValue = 9999;

            if ($deduction > 0) {
                $calculatedMax = floor($weight / $deduction);
                $maxMistakesText = (string) $calculatedMax;
                $maxMistakesValue = $calculatedMax;
            }

            // Build helper text
            $helperText = __('Weight') . ': ' . $weight . '% | ' .
                         __('Deduction Per Mistake') . ': ' . $deduction;

            if ($failThreshold > 0) {
                $helperText .= ' | ' . __('Fail Threshold') . ': ' . $failThreshold;
            }

            $fields[] = TextInput::make($aspectKey)
                ->label($config['label'])
                ->prefix(__('Maximum Mistakes') . ': ' . $maxMistakesText)
                ->suffix($deduction > 0 ? (string) $deduction : null)
                ->helperText($helperText)
                ->minValue(0)
                ->maxValue($maxMistakesValue)
                ->numeric()
                ->step(1)
                ->default($ratings[$aspectKey]?->mistakes ?? 0);
        }

        return $fields;
    }
}
