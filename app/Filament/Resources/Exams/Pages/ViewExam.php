<?php

declare(strict_types=1);

namespace App\Filament\Resources\Exams\Pages;

use App\Filament\Resources\Exams\ExamResource;
use Filament\Resources\Pages\ViewRecord;

class ViewExam extends ViewRecord
{
    protected static string $resource = ExamResource::class;

    protected string $view = 'filament.resources.exams.pages.view-exam';

    public static function getNavigationLabel(): string
    {
        return __('View Exam');
    }

    public function getTitle(): string
    {
        return __('View Exam');
    }
}
