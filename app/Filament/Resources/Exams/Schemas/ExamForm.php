<?php

declare(strict_types=1);

namespace App\Filament\Resources\Exams\Schemas;

use App\Models\Classroom;
use Exception;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class ExamForm
{
    /**
     * @throws Exception
     */
    public static function configure(Schema $schema): Schema
    {
        return $schema->components([
            TextInput::make('name')
                ->label('Name')
                ->translateLabel()
                ->required()
                ->maxLength(255),
            Select::make('teacher_id')
                ->label('Teacher')
                ->translateLabel()
                ->relationship('teacher', 'name')
                ->live(true)
                ->preload()
                ->searchable()
                ->required(),
            Select::make('classroom_id')
                ->label('Classroom')
                ->translateLabel()
                ->disabled(function ($get) {
                    return $get('teacher_id') === null;
                })
                ->options(function ($get) {
                    return $get('teacher_id')
                        ? Classroom::where(
                            'teacher_id',
                            $get('teacher_id'),
                        )->pluck('name', 'id')
                        : [];
                })
                ->preload()
                ->searchable()
                ->required(),
        ]);
    }
}
