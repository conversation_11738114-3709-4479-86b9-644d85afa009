<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\GuardianResource\Pages\CreateGuardian;
use App\Filament\Resources\GuardianResource\Pages\EditGuardian;
use App\Filament\Resources\GuardianResource\Pages\ListGuardians;
use App\Filament\Resources\GuardianResource\Pages\ManegeStudents;
use App\Models\Guardian;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use UnitEnum;

class GuardianResource extends Resource
{
    protected static ?string $model = Guardian::class;

    protected static string|null|BackedEnum $navigationIcon = 'heroicon-o-user-group';

    protected static string|UnitEnum|null $navigationGroup = 'School Management';

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    protected static ?int $navigationSort = 1;

    public static function getPluralModelLabel(): string
    {
        return __('Guardians');
    }

    public static function getLabel(): string
    {
        return __('Guardian');
    }

    public static function getNavigationGroup(): ?string
    {
        return __(static::$navigationGroup);
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make(__('Guardian Information'))
                    ->description(__('Enter the guardian\'s personal details'))
                    ->schema([
                        TextInput::make('name')
                            ->label('Full Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255)
                            ->placeholder(__('Enter full name')),

                        TextInput::make('email')
                            ->label('Email Address')
                            ->translateLabel()
                            ->email()
                            ->maxLength(255)
                            ->placeholder(__('<EMAIL>')),

                        TextInput::make('phone')
                            ->label('Phone Number')
                            ->translateLabel()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder(__('002189123456789')),

                        Textarea::make('address')
                            ->label('Address')
                            ->translateLabel()
                            ->rows(3)
                            ->placeholder(__('Enter complete address')),
                    ])->columns(2),

                Section::make(__('Additional Information'))
                    ->schema([
                        TextInput::make('national_id')
                            ->label('National ID')
                            ->translateLabel()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder(__('National ID number')),

                        TextInput::make('emergency_contact')
                            ->label('Emergency Contact')
                            ->translateLabel()
                            ->maxLength(255)
                            ->placeholder(__('Emergency contact number')),

                        Select::make('relationship_to_student')
                            ->label('Relationship to Student')
                            ->translateLabel()
                            ->options([
                                'Father' => __('Father'),
                                'Mother' => __('Mother'),
                                'Grandfather' => __('Grandfather'),
                                'Grandmother' => __('Grandmother'),
                                'Uncle' => __('Uncle'),
                                'Aunt' => __('Aunt'),
                                'Guardian' => __('Legal Guardian'),
                                'Other' => __('Other'),
                            ])
                            ->default('Father')
                            ->required(),

                        Textarea::make('notes')
                            ->label('Notes')
                            ->translateLabel()
                            ->rows(3)
                            ->placeholder(__('Any additional notes about the guardian')),
                    ])->columns(2),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Full Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('email')
                    ->label('Email Address')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('phone')
                    ->label('Phone Number')
                    ->translateLabel()
                    ->searchable(),

                TextColumn::make('relationship_to_student')
                    ->label('Relationship to Student')
                    ->translateLabel()
                    ->searchable()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'Father' => 'success',
                        'Mother' => 'info',
                        'Guardian' => 'warning',
                        default => 'gray',
                    }),

                TextColumn::make('students_count')
                    ->label('Students')
                    ->translateLabel()
                    ->counts('students')
                    ->badge()
                    ->color('primary'),

                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('relationship_to_student')
                    ->label('Relationship to Student')
                    ->translateLabel()
                    ->options([
                        'Father' => __('Father'),
                        'Mother' => __('Mother'),
                        'Grandfather' => __('Grandfather'),
                        'Grandmother' => __('Grandmother'),
                        'Uncle' => __('Uncle'),
                        'Aunt' => __('Aunt'),
                        'Guardian' => __('Legal Guardian'),
                        'Other' => __('Other'),
                    ]),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditGuardian::class,
            ManegeStudents::class,
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListGuardians::route('/'),
            'create' => CreateGuardian::route('/create'),
            'edit' => EditGuardian::route('/{record}/edit'),
            'students' => ManegeStudents::route('/{record}/students'),
        ];
    }
}
