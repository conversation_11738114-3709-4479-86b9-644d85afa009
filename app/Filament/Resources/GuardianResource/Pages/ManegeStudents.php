<?php

declare(strict_types=1);

namespace App\Filament\Resources\GuardianResource\Pages;

use App\Filament\Resources\GuardianResource;
use App\Models\Classroom;
use App\Models\Guardian;
use App\Models\Student;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;

class ManegeStudents extends ManageRelatedRecords
{
    protected static string $resource = GuardianResource::class;

    protected static string $relationship = 'students';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-user-group';

    public static function getNavigationLabel(): string
    {
        return __('Students');
    }

    public function getTitle(): string
    {
        return __('Students');
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Group::make([])->schema([
                    Section::make(__('Student Information'))
                        ->schema([
                            TextInput::make('name')
                                ->label('Full Name')
                                ->translateLabel()
                                ->required()
                                ->maxLength(255)
                                ->placeholder(__('Enter student full name')),

                            TextInput::make('age')
                                ->label('Age')
                                ->translateLabel()
                                ->numeric()
                                ->required(),

                            DatePicker::make('date_of_birth')
                                ->label('Date of Birth')
                                ->translateLabel()
                                ->required()
                                ->maxDate(now())
                                ->displayFormat('Y-m-d'),

                            Select::make('gender')
                                ->label('Gender')
                                ->translateLabel()
                                ->options([
                                    'male' => __('Male'),
                                    'female' => __('Female'),
                                ])
                                ->required(),

                            TextInput::make('grade_level')
                                ->label('Grade Level')
                                ->translateLabel()
                                ->maxLength(255)
                                ->placeholder(__('e.g., Grade 5, High School')),

                            Select::make('classroom_id')
                                ->label('Classroom')
                                ->translateLabel()
                                ->options(Classroom::all()->pluck('name', 'id'))
                                ->disabled()
                                ->searchable(),

                            TextInput::make('registration_number')
                                ->label('Registration Number')
                                ->translateLabel()
                                ->unique(Student::class, 'registration_number', ignoreRecord: true)
                                ->disabled()
                                ->columnSpan(2),

                        ])->columns(2),

                    Section::make(__('Additional Information'))
                        ->schema([
                            Textarea::make('medical_conditions')
                                ->label('Medical Conditions')
                                ->translateLabel()
                                ->rows(3)
                                ->placeholder(__('Any medical conditions or allergies')),

                            Textarea::make('notes')
                                ->label('Notes')
                                ->translateLabel()
                                ->rows(3)
                                ->placeholder(__('Additional notes about the student')),
                        ]),
                ])->columnSpan(2),
                Group::make([
                    Section::make(__('Student Profile & Media'))
                        ->description(__('Manage student status, guardian details, and upload personal documents and photos.'))
                        ->schema([
                            Select::make('is_active')
                                ->label('Active Student')
                                ->translateLabel()
                                ->options([
                                    1 => __('Active'),
                                    0 => __('Inactive'),
                                ])
                                ->default(1),
                            Select::make('guardian_id')
                                ->label('Guardian')
                                ->translateLabel()
                                ->options(
                                    function () {
                                        $guardian = $this->getRecord();

                                        return Guardian::where('id', $guardian->id)->pluck('name', 'id');
                                    }
                                )
                                ->default(function () {
                                    $guardian = $this->getRecord();

                                    return $guardian->id;
                                })
                                ->required(),
                            SpatieMediaLibraryFileUpload::make('student_photo')
                                ->collection('student_photo')
                                ->label('Personal Photo')
                                ->translateLabel()
                                ->disk('local')
                                ->visibility('private')
                                ->downloadable(),
                            SpatieMediaLibraryFileUpload::make('student_documents')
                                ->collection('student_documents')
                                ->label('Documents')
                                ->translateLabel()
                                ->disk('local')
                                ->multiple()
                                ->visibility('private')
                                ->downloadable(),
                        ]),
                ])->columnSpan(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->label('Full Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('age')
                    ->label('Age')
                    ->translateLabel()
                    ->sortable(),

                TextColumn::make('date_of_birth')
                    ->label('Date of Birth')
                    ->translateLabel()
                    ->date()
                    ->sortable(),

                TextColumn::make('gender')
                    ->label('Gender')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'male' => __('Male'),
                        'female' => __('Female'),
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'male' => 'info',
                        'female' => 'warning',
                        default => 'gray',
                    }),

                TextColumn::make('grade_level')
                    ->label('Grade Level')
                    ->translateLabel()
                    ->searchable(),

                IconColumn::make('is_active')
                    ->label('Active Status')
                    ->translateLabel()
                    ->boolean()
                    ->label('Active')
                    ->translateLabel(),

                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('gender')
                    ->label('Gender')
                    ->translateLabel()
                    ->options([
                        'male' => __('Male'),
                        'female' => __('Female'),
                    ]),

                TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->translateLabel(),
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('Add Student')
                    ->translateLabel()
                    ->slideOver(),
            ])
            ->recordActions([
                EditAction::make()->slideOver(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
