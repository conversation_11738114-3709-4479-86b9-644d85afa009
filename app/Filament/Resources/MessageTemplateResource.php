<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\MessageTemplateResource\Pages\CreateMessageTemplate;
use App\Filament\Resources\MessageTemplateResource\Pages\EditMessageTemplate;
use App\Filament\Resources\MessageTemplateResource\Pages\ListMessageTemplates;
use App\Models\MessageTemplate;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class MessageTemplateResource extends Resource
{
    protected static ?string $model = MessageTemplate::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-clipboard-document-list';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                TextInput::make('code')
                    ->label('Code')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
                Repeater::make('params')
                    ->label('Parameters')
                    ->translateLabel()
                    ->schema([
                        TextInput::make('key')
                            ->label('Key')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255),
                    ])
                    ->default([
                        ['key' => 'name'],
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label('Code')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('params')
                    ->label('Parameters')
                    ->translateLabel()
                    ->formatStateUsing(function ($record) {
                        return count($record->params);
                    }),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListMessageTemplates::route('/'),
            'create' => CreateMessageTemplate::route('/create'),
            'edit' => EditMessageTemplate::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Message Templates');
    }

    public static function getLabel(): string
    {
        return __('Message Template');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('System Management');
    }
}
