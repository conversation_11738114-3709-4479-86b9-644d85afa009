<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\QuestionResource\Pages\CreateQuestion;
use App\Filament\Resources\QuestionResource\Pages\EditQuestion;
use App\Filament\Resources\QuestionResource\Pages\ListQuestions;
use App\Models\Question;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;

class QuestionResource extends Resource
{
    protected static ?string $model = Question::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-swatch';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('type')
                    ->label('Question Type')
                    ->translateLabel()
                    ->searchable()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => ucfirst(__($state)))
                    ->color(fn (string $state): string => match ($state) {
                        'global' => 'success',
                        'personal' => 'warning',
                        default => 'gray',
                    })
                    ->sortable(),
                TextColumn::make('teacher.name')
                    ->label('Teacher')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('verse.text')
                    ->label('Verse')
                    ->translateLabel()
                    ->searchable()
                    ->sortable()
                    ->wrap()
                    ->formatStateUsing(fn (string $state): HtmlString => new HtmlString(
                        '<div class="arabic-verse-text" style="font-family: \'UthmanicQaloun\', \'qaloon-v8\', serif; font-size: 18px; line-height: 1.8; text-align: right; direction: rtl;">'.
                        e($state).
                        '</div>'
                    ))
                    ->html(),
                TextColumn::make('startVerse.text')
                    ->label('From Verse')
                    ->translateLabel()
                    ->searchable()
                    ->sortable()
                    ->wrap()
                    ->formatStateUsing(fn (string $state): HtmlString => new HtmlString(
                        '<div class="arabic-verse-text" style="font-family: \'UthmanicQaloun\', \'qaloon-v8\', serif; font-size: 18px; line-height: 1.8; text-align: right; direction: rtl;">'.
                        e($state).
                        '</div>'
                    ))
                    ->html(),
                TextColumn::make('endVerse.text')
                    ->label('To Verse')
                    ->translateLabel()
                    ->searchable()
                    ->sortable()
                    ->wrap()
                    ->formatStateUsing(fn (string $state): HtmlString => new HtmlString(
                        '<div class="arabic-verse-text" style="font-family: \'UthmanicQaloun\', \'qaloon-v8\', serif; font-size: 18px; line-height: 1.8; text-align: right; direction: rtl;">'.
                        e($state).
                        '</div>'
                    ))
                    ->html(),
                TextColumn::make('status')
                    ->label('Question Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => ucfirst(__($state)))
                    ->color(fn (string $state): string => match ($state) {
                        'draft' => 'warning',
                        'published' => 'success',
                        'archived' => 'danger',
                        default => 'gray',
                    })
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListQuestions::route('/'),
            'create' => CreateQuestion::route('/create'),
            'edit' => EditQuestion::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Questions');
    }

    public static function getLabel(): string
    {
        return __('Question');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('System Management');
    }
}
