<?php

declare(strict_types=1);

namespace App\Filament\Resources\QuestionResource;

use App\Models\Verse;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

class QuestionForm
{
    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('type')
                    ->label('Question Type')
                    ->translateLabel()
                    ->live()
                    ->options([
                        'global' => __('Global'),
                        'personal' => __('Personal'),
                    ])
                    ->required(),
                Select::make('teacher_id')
                    ->label('Teacher')
                    ->translateLabel()
                    ->relationship('teacher', 'name')
                    ->preload()
                    ->visible(function (Get $get): bool {
                        return $get('type') === 'personal';
                    })
                    ->required()
                    ->searchable(),

                // select the eighth system or start_end system
                Select::make('system')
                    ->label('Question System')
                    ->translateLabel()
                    ->live()
                    ->default(function (Get $get): string {
                        if ($get('verse_id')) {
                            return 'eighth';
                        }

                        if ($get('start_verse_id') || $get('end_verse_id')) {
                            return 'start_end';
                        }

                        return 'start_end'; // fallback default
                    })
                    ->options([
                        'eighth' => __('Eighth'),
                        'start_end' => __('Start End'),
                    ])
                    ->required(),

                Section::make(__('Verses'))
                    ->visible(function (Get $get): bool {
                        return $get('system') === 'eighth' || $get('system') === 'start_end';
                    })
                    ->schema([
                        Select::make('verse_id')
                            ->label('Verse')
                            ->translateLabel()
                            ->options(
                                Verse::where('eighth', 1)->pluck('text', 'id')
                            )
                            ->preload()
                            ->searchable()
                            ->extraAttributes([
                                'class' => 'arabic-select-wrapper',
                                'style' => 'font-family: "UthmanicQaloun", "qaloon-v8", serif; text-align: right; direction: rtl;',
                            ])
                            ->visible(function (Get $get): bool {
                                return $get('system') === 'eighth';
                            })
                            ->required(),

                        Select::make('start_verse_id')
                            ->label('From Verse')
                            ->translateLabel()
                            ->relationship('startVerse', 'text')
                            ->searchable()
                            ->extraAttributes([
                                'class' => 'arabic-select-wrapper',
                                'style' => 'font-family: "UthmanicQaloun", "qaloon-v8", serif; text-align: right; direction: rtl;',
                            ])
                            ->suffixAction(
                                Action::make('selectStartVerse')
                                    ->icon('heroicon-m-magnifying-glass')
                                    ->modalHeading('Select Start Verse')
                                    ->modalWidth('4xl')
                                    ->slideOver()
                                    ->closeModalByClickingAway()
                                    ->closeModalByEscaping()
                                    ->modalSubmitActionLabel(__('Select Verse'))
                                    ->action(function () {
                                        $this->form->fill([
                                            'system' => 'start_end',
                                            'start_verse_id' => $this->startVerseId,
                                            'end_verse_id' => $this->endVerseId,
                                        ]);
                                    })
                                    ->modalContent(fn () => new HtmlString(
                                        Blade::render('@livewire("verse-selector", ["fieldName" => "start_verse_id"])')
                                    ))
                            )
                            ->visible(function (Get $get): bool {
                                return $get('system') === 'start_end';
                            })
                            ->required(),

                        Select::make('end_verse_id')
                            ->label('To Verse')
                            ->translateLabel()
                            ->relationship('endVerse', 'text')
                            ->searchable()
                            ->extraAttributes([
                                'class' => 'arabic-select-wrapper',
                                'style' => 'font-family: "UthmanicQaloun", "qaloon-v8", serif; text-align: right; direction: rtl;',
                            ])
                            ->suffixAction(
                                Action::make('selectEndVerse')
                                    ->icon('heroicon-m-magnifying-glass')
                                    ->modalHeading('Select End Verse')
                                    ->modalWidth('4xl')
                                    ->slideOver()
                                    ->closeModalByClickingAway()
                                    ->closeModalByEscaping()
                                    ->modalSubmitActionLabel(__('Select Verse'))
                                    ->action(function () {
                                        $this->form->fill([
                                            'system' => 'start_end',
                                            'start_verse_id' => $this->startVerseId,
                                            'end_verse_id' => $this->endVerseId,
                                        ]);
                                    })
                                    ->modalContent(fn () => new HtmlString(
                                        Blade::render('@livewire("verse-selector", ["fieldName" => "end_verse_id"])')
                                    ))
                            )
                            ->visible(function (Get $get): bool {
                                return $get('system') === 'start_end';
                            })
                            ->required(),
                    ])->columns(2),

                Select::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->options([
                        'draft' => __('Draft'),
                        'published' => __('Published'),
                        'archived' => __('Archived'),
                    ])
                    ->required(),
            ])->columns(1);
    }
}
