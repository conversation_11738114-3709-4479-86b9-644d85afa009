<?php

declare(strict_types=1);

namespace App\Filament\Resources\Receipts\Schemas;

use App\Models\Receipt;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ReceiptForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make(__('Receipt Information'))
                    ->schema([
                        Group::make([
                            Select::make('student_id')
                                ->label(__('Student'))
                                ->relationship('student', 'name')
                                ->searchable()
                                ->preload()
                                ->required()
                                ->columnSpan(1),

                            TextInput::make('receipt_number')
                                ->label(__('Receipt Number'))
                                ->required()
                                ->unique(
                                    Receipt::class,
                                    'receipt_number',
                                    ignoreRecord: true,
                                )
                                ->default(
                                    fn () => Receipt::generateReceiptNumber(),
                                )
                                ->disabled(
                                    fn (?Receipt $record) => $record !== null,
                                )
                                ->dehydrated()
                                ->columnSpan(1),
                        ])->columns(2),

                        Group::make([
                            TextInput::make('payer_name')
                                ->label(__('Payer Name'))
                                ->required()
                                ->maxLength(255)
                                ->placeholder(
                                    __(
                                        'Enter the name of the person who made the payment',
                                    ),
                                )
                                ->columnSpan(1),

                            TextInput::make('payment_amount')
                                ->label(__('Payment Amount'))
                                ->required()
                                ->numeric()
                                ->minValue(0.01)
                                ->step(0.01)
                                ->prefix(__('LYD'))
                                ->placeholder('0.00')
                                ->columnSpan(1),
                        ])->columns(2),

                        DatePicker::make('payment_date')
                            ->label(__('Payment Date'))
                            ->required()
                            ->default(now())
                            ->maxDate(now())
                            ->displayFormat('Y-m-d'),

                        Textarea::make('notes')
                            ->label(__('Notes'))
                            ->placeholder(
                                __('Additional notes about this payment...'),
                            )
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ])
            ->columns(1);
    }
}
