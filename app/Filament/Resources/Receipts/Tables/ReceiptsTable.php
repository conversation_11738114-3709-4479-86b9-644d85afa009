<?php

declare(strict_types=1);

namespace App\Filament\Resources\Receipts\Tables;

use Filament\Actions\BulkAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class ReceiptsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('receipt_number')
                    ->label(__('Receipt Number'))
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('bold'),

                TextColumn::make('student.name')
                    ->label(__('Student'))
                    ->searchable()
                    ->sortable()
                    ->description(
                        fn($record) => $record->student?->guardian?->name ??
                            __('No Guardian'),
                    ),

                TextColumn::make('payer_name')
                    ->label(__('Payer Name'))
                    ->searchable()
                    ->sortable(),

                TextColumn::make('payment_amount')
                    ->label(__('Amount'))
                    ->formatStateUsing(
                        fn(float $state): string => number_format($state, 2) .
                            ' ' .
                            'د.ل',
                    )
                    ->sortable(),
                TextColumn::make('payment_date')
                    ->label(__('Payment Date'))
                    ->date('Y-m-d')
                    ->sortable()
                    ->description(
                        fn($record) => $record->payment_date->diffForHumans(),
                    ),

                TextColumn::make('student.classroom.name')
                    ->label(__('Classroom'))
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('student_id')
                    ->label(__('Student'))
                    ->relationship('student', 'name')
                    ->searchable()
                    ->preload(),

                Filter::make('payment_date')
                    ->label(__('Payment Date'))
                    ->schema([
                        DatePicker::make('payment_date_from')->label(
                            __('From Date'),
                        ),
                        DatePicker::make('payment_date_until')->label(
                            __('Until Date'),
                        ),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['payment_date_from'],
                                fn(
                                    Builder $query,
                                    $date,
                                ): Builder => $query->whereDate(
                                    'payment_date',
                                    '>=',
                                    $date,
                                ),
                            )
                            ->when(
                                $data['payment_date_until'],
                                fn(
                                    Builder $query,
                                    $date,
                                ): Builder => $query->whereDate(
                                    'payment_date',
                                    '<=',
                                    $date,
                                ),
                            );
                    }),

                SelectFilter::make('month')
                    ->label(__('Month'))
                    ->options([
                        '01' => __('January'),
                        '02' => __('February'),
                        '03' => __('March'),
                        '04' => __('April'),
                        '05' => __('May'),
                        '06' => __('June'),
                        '07' => __('July'),
                        '08' => __('August'),
                        '09' => __('September'),
                        '10' => __('October'),
                        '11' => __('November'),
                        '12' => __('December'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(
                                Builder $query,
                                $month,
                            ): Builder => $query->whereMonth(
                                'payment_date',
                                $month,
                            ),
                        );
                    }),
            ])
            ->recordActions([
                ViewAction::make()->label(''),
                EditAction::make()->label(''),
                Action::make('print')
                    ->label('')
                    ->icon('heroicon-o-printer')
                    ->color('success')
                    ->url(fn($record) => route('receipts.print', $record))
                    ->openUrlInNewTab(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    BulkAction::make('bulk_print')
                        ->label(__('Print Selected'))
                        ->icon('heroicon-o-printer')
                        ->color('success')
                        ->action(function (Collection $records) {
                            $ids = $records->pluck('id')->values()->all();
                            $token = Str::uuid()->toString();
                            $userId = auth()->id() ?? 'guest';
                            $ttl = now()->addMinutes(10);

                            $cacheKey = "bulk-print:{$userId}:{$token}";
                            Cache::put($cacheKey, $ids, $ttl);

                            $url = URL::temporarySignedRoute(
                                'receipts.bulk-print',
                                $ttl,
                                ['token' => $token],
                            );

                            return redirect()->to($url); // open in the same tab
                            // If you prefer a new tab: dispatch event & window.open($url)
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ])
            ->defaultSort('payment_date', 'desc')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }
}
