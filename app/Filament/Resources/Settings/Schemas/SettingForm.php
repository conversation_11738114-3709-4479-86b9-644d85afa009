<?php

declare(strict_types=1);

namespace App\Filament\Resources\Settings\Schemas;

use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class SettingForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('key')
                    ->label('Key')
                    ->translateLabel()
                    ->required()
                    ->disabled()
                    ->maxLength(255),
                KeyValue::make('value')
                    ->label('Value')
                    ->translateLabel()
                    ->keyLabel(__('Key'))
                    ->valueLabel(__('Value'))
                    ->deletable(false)
                    ->editableKeys(false)
                    ->addable(false)
                    ->columnSpanFull(),
            ])->columns(1);
    }
}
