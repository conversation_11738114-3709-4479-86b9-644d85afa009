<?php

declare(strict_types=1);

namespace App\Filament\Resources\SmsMessageResource\Pages;

use App\Enum\MessageStatus;
use App\Filament\Resources\SmsMessageResource;
use App\Models\MessageTemplate;
use Filament\Resources\Pages\CreateRecord;

class CreateSmsMessage extends CreateRecord
{
    protected static string $resource = SmsMessageResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['status'] = MessageStatus::Pending->value;
        $code = MessageTemplate::find($data['template_id'])->code ?? '';
        $data['template_id'] = $code;

        return $data;
    }
}
