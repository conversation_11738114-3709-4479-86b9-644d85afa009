<?php

declare(strict_types=1);

namespace App\Filament\Resources\SmsMessageResource\Pages;

use App\Filament\Resources\SmsMessageResource;
use Filament\Infolists\Components\KeyValueEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Resources\Pages\ViewRecord;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ViewSmsMessage extends ViewRecord
{
    protected static string $resource = SmsMessageResource::class;

    public function infolist(Schema $schema): Schema
    {
        return $infolist
            ->schema([
                Section::make(__('Message Details'))
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('id')
                                    ->label('Message ID')
                                    ->translateLabel(),

                                TextEntry::make('template_id')
                                    ->label('Template ID')
                                    ->translateLabel(),

                                TextEntry::make('sender')
                                    ->label('Sender')
                                    ->translateLabel(),

                                TextEntry::make('receiver')
                                    ->label('Receiver')
                                    ->translateLabel(),

                                TextEntry::make('payment_type')
                                    ->label('Payment Type')
                                    ->translateLabel()
                                    ->badge()
                                    ->formatStateUsing(fn ($state) => __($state))
                                    ->color(fn ($state) => match ($state) {
                                        'prepaid' => 'success',
                                        'postpaid' => 'warning',
                                        default => 'gray',
                                    }),

                                TextEntry::make('message_id')
                                    ->label('Message System ID')
                                    ->translateLabel()
                                    ->copyable(),
                            ]),
                    ]),

                Section::make(__('Message Status'))
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('status')
                                    ->label('Internal Status')
                                    ->translateLabel()
                                    ->badge()
                                    ->formatStateUsing(fn ($state) => __($state))
                                    ->color(fn ($state) => match ($state) {
                                        'pending' => 'warning',
                                        'sent' => 'info',
                                        'failed' => 'danger',
                                        'delivered' => 'success',
                                        default => 'gray',
                                    }),

                                TextEntry::make('delivery_status')
                                    ->label('Delivery Status')
                                    ->translateLabel()
                                    ->badge()
                                    ->formatStateUsing(fn ($state) => __($state))
                                    ->color(fn ($state) => match ($state) {
                                        'delivered' => 'success',
                                        'pending' => 'warning',
                                        'failed' => 'danger',
                                        default => 'gray',
                                    }),

                                TextEntry::make('delivered_at')
                                    ->label('Delivered At')
                                    ->translateLabel()
                                    ->dateTime(),
                            ]),
                    ]),

                Section::make(__('Meta'))
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('created_at')
                                    ->label('Created At')
                                    ->translateLabel()
                                    ->dateTime(),

                                TextEntry::make('updated_at')
                                    ->label('Last Updated')
                                    ->translateLabel()
                                    ->dateTime(),
                            ]),
                    ]),
                Section::make(__('Raw Data'))
                    ->schema([
                        KeyValueEntry::make('params')
                            ->label('Parameters')
                            ->translateLabel(),

                        KeyValueEntry::make('api_response')
                            ->label('API Response')
                            ->translateLabel(),
                    ]),
            ]);
    }
}
