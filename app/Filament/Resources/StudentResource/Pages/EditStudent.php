<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Pages;

use App\Filament\Resources\StudentResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditStudent extends EditRecord
{
    protected static string $resource = StudentResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit Student');
    }

    public function getTitle(): string
    {
        return __('Edit Student');
    }

    protected function getHeaderActions(): array
    {
        return [DeleteAction::make()];
    }
}
