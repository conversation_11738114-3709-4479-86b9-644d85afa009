<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Pages;

use App\Filament\Resources\Exams\ExamResource;
use App\Filament\Resources\StudentResource;
use BackedEnum;
use Exception;
use Filament\Actions\Action;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Support\Enums\IconPosition;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ManageExams extends ManageRelatedRecords
{
    protected static string $resource = StudentResource::class;

    protected static string $relationship = 'exams';

    protected static string|null|BackedEnum $navigationIcon = 'heroicon-o-clipboard-document';

    public static function getNavigationLabel(): string
    {
        return __('Exams');
    }

    public function getTitle(): string
    {
        return __('Exams');
    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(
                fn ($query) => $query->with(['exam', 'questions']),
            )
            ->columns([
                TextColumn::make('exam.name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('exam.teacher.name')
                    ->label('Teacher')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('exam.classroom.name')
                    ->label('Classroom')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('total_score')
                    ->label('Total Score')
                    ->translateLabel()
                    ->formatStateUsing(function ($record) {
                        $questionCount = $record->questions->count();
                        if ($questionCount === 0) {
                            return __('N/A');
                        }
                        $average = $record->total_score / $questionCount;

                        return number_format($average, 2);
                    })
                    ->icon('iconsax-lin-percentage-square')
                    ->iconPosition(IconPosition::After)
                    ->sortable()
                    ->translateLabel(),
                TextColumn::make('exam.created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime(),
                TextColumn::make('exam.updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime(),
            ])
            ->recordActions([
                Action::make('viewResults')
                    ->label('View Results')
                    ->translateLabel()
                    ->icon('heroicon-o-eye')
                    ->url(function ($record) {
                        // get route from ExamResource::getPages()
                        return ExamResource::getUrl('view', [
                            'record' => $record->exam->id,
                        ]);
                    }),
            ]);
    }
}
