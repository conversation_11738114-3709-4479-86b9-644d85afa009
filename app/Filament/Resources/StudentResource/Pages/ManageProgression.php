<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Pages;

use App\Filament\Resources\StudentResource;
use App\Models\Chapter;
use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\Verse;
use BackedEnum;
use Exception;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class ManageProgression extends ManageRelatedRecords
{
    protected static string $relationship = 'progression';

    protected static string $resource = StudentResource::class;

    protected static string|null|BackedEnum $navigationIcon = 'iconsax-lin-percentage-square';

    public static function getNavigationLabel(): string
    {
        return __('Manage Progression');
    }

    public function getTitle(): string
    {
        return __('Manage Progression');
    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        /* @var Student $student */
        $student = $this->record;

        return $table
            ->query(
                Chapter::query()
                    ->withCount('verses') // total_verses
                    ->with([
                        'verses' => function ($query) use ($student) {
                            $query->whereHas(
                                'studentProgressions',
                                fn ($q) => $q->where('student_id', $student->id),
                            );
                        },
                    ])
                    ->with([
                        'verses.studentProgressions' => fn ($q) => $q->where(
                            'student_id',
                            $student->id,
                        ),
                    ])
                    ->orderBy('number'),
            )
            ->columns([
                TextColumn::make('name')
                    ->label('Chapter')
                    ->translateLabel()
                    ->sortable()
                    ->searchable(),

                TextColumn::make('progress')
                    ->label('Progress (%)')
                    ->translateLabel()
                    ->getStateUsing(function (Chapter $record) {
                        $total = $record->verses_count;
                        $completed = $record->verses
                            ->flatMap(fn ($verse) => $verse->studentProgressions)
                            ->count();

                        if ($total === 0) {
                            return '0%';
                        }

                        return round(($completed / $total) * 100, 2).'%';
                    }),

                TextColumn::make('last_updated')
                    ->label('Last Updated')
                    ->translateLabel()
                    ->getStateUsing(function (Chapter $record) use ($student) {
                        $timestamps = $record->verses
                            ->flatMap(
                                fn (
                                    $verse,
                                ) => $verse->studentProgressions->where(
                                    'student_id',
                                    $student->id,
                                ),
                            )
                            ->pluck('updated_at')
                            ->filter()
                            ->sortDesc();

                        return $timestamps->first()?->format('Y-m-d H:i') ??
                            '-';
                    }),
            ])
            ->headerActions([
                Action::make('addProgression')
                    ->label('Add Progression')
                    ->translateLabel()
                    ->icon('heroicon-m-plus')
                    ->schema([
                        Select::make('chapter_id')
                            ->label('Chapter')
                            ->translateLabel()
                            ->searchable()
                            ->preload()
                            ->options(
                                Chapter::query()
                                    ->orderBy('number')
                                    ->pluck('name', 'id'),
                            )
                            ->reactive(),

                        Select::make('verse_from')
                            ->label('Verse From')
                            ->translateLabel()
                            ->required()
                            ->searchable()
                            ->extraAttributes(['class' => 'quran-font'])
                            ->optionsLimit(500)
                            ->visible(function (callable $get): bool {
                                if (! $get('chapter_id')) {
                                    return false;
                                }

                                return true;
                            })
                            ->options(function (callable $get): Collection {
                                $chapterId = $get('chapter_id');

                                return Verse::query()
                                    ->when(
                                        $chapterId,
                                        fn ($q) => $q->where(
                                            'chapter_id',
                                            $chapterId,
                                        ),
                                    )
                                    ->orderBy('number')
                                    ->get()
                                    ->mapWithKeys(
                                        fn ($verse) => [
                                            $verse->id => $verse->number.
                                                ': '.
                                                $verse->text,
                                        ],
                                    );
                            })
                            ->reactive(),

                        Select::make('verse_to')
                            ->label('Verse To')
                            ->translateLabel()
                            ->searchable()
                            ->optionsLimit(500)
                            ->extraAttributes(['class' => 'quran-font'])
                            ->visible(function (callable $get): bool {
                                if (! $get('chapter_id')) {
                                    return false;
                                }

                                return true;
                            })
                            ->options(function (callable $get): Collection {
                                $chapterId = $get('chapter_id');

                                return Verse::query()
                                    ->when(
                                        $chapterId,
                                        fn ($q) => $q->where(
                                            'chapter_id',
                                            $chapterId,
                                        ),
                                    )
                                    ->orderBy('number')
                                    ->get()
                                    ->mapWithKeys(
                                        fn ($verse) => [
                                            $verse->id => $verse->number.
                                                ': '.
                                                $verse->text,
                                        ],
                                    );
                            }),
                    ])
                    ->action(function (array $data) {
                        $this->syncProgression($data);
                    }),
            ]);
    }

    /**
     * @throws Exception
     */
    public function syncProgression($data): void
    {
        try {
            $student = $this->record;
            $fromId = $data['verse_from'];
            $toId = $data['verse_to'] ?? null;

            $fromVerse = Verse::findOrFail($fromId);
            $toVerse = $toId ? Verse::findOrFail($toId) : null;

            if ($toVerse && $fromVerse->chapter_id !== $toVerse->chapter_id) {
                throw new Exception(
                    'Selected verses must be in the same chapter.',
                );
            }

            $chapterId = $fromVerse->chapter_id;

            $startNumber = min(
                $fromVerse->number,
                $toVerse?->number ?? $fromVerse->number,
            );

            $endNumber = max(
                $fromVerse->number,
                $toVerse?->number ?? $fromVerse->number,
            );

            // Get all verse IDs in the selected range of that chapter
            $verseIds = Verse::query()
                ->where('chapter_id', $chapterId)
                ->whereBetween('number', [$startNumber, $endNumber])
                ->pluck('id')
                ->toArray();

            if (empty($verseIds)) {
                throw new Exception('No verses found in the selected range.');
            }

            // Get only the existing progression IDs in the selected chapter
            $existingVerseIds = StudentProgression::query()
                ->where('student_id', $student->id)
                ->whereIn('verse_id', function ($query) use ($chapterId) {
                    $query
                        ->select('id')
                        ->from('verses')
                        ->where('chapter_id', $chapterId);
                })
                ->pluck('verse_id')
                ->toArray();

            // Calculate changes limited to this chapter
            $toInsert = array_diff($verseIds, $existingVerseIds);
            $toDelete = array_diff($existingVerseIds, $verseIds);

            if (! empty($toInsert)) {
                $insertData = array_map(
                    fn ($id) => [
                        'id' => (string) Str::uuid(),
                        'student_id' => $student->id,
                        'verse_id' => $id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ],
                    $toInsert,
                );

                StudentProgression::insert($insertData);
            }

            if (! empty($toDelete)) {
                StudentProgression::query()
                    ->where('student_id', $student->id)
                    ->whereIn('verse_id', $toDelete)
                    ->delete();
            }

            Notification::make()
                ->title(__('Progression synced successfully'))
                ->success()
                ->send();
        } catch (Exception $e) {
            Notification::make()
                ->title(__('Failed to sync progression'))
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
