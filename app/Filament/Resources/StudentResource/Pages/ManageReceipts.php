<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Pages;

use App\Filament\Resources\StudentResource;
use App\Models\Receipt;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ManageReceipts extends ManageRelatedRecords
{
    protected static string $resource = StudentResource::class;

    protected static string $relationship = 'receipts';

    protected static string|null|BackedEnum $navigationIcon = 'heroicon-o-banknotes';

    public static function getNavigationLabel(): string
    {
        return __('Receipts');
    }

    public function getTitle(): string
    {
        $student = $this->getOwnerRecord();

        return __('Manage Receipts')." - {$student->name}";
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('receipt_number')
            ->columns([
                TextColumn::make('receipt_number')
                    ->label(__('Receipt Number'))
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('bold'),

                TextColumn::make('payer_name')
                    ->label(__('Payer Name'))
                    ->searchable()
                    ->sortable(),

                TextColumn::make('payment_amount')
                    ->label(__('Amount'))
                    ->formatStateUsing(
                        fn (float $state): string => number_format($state, 2).
                            ' '.
                            'د.ل',
                    )
                    ->sortable(),

                TextColumn::make('payment_date')
                    ->label(__('Payment Date'))
                    ->date('Y-m-d')
                    ->sortable()
                    ->description(
                        fn ($record) => $record->payment_date->diffForHumans(),
                    ),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('payment_date')
                    ->label(__('Payment Date'))
                    ->schema([
                        DatePicker::make('payment_date_from')->label(
                            __('From Date'),
                        ),
                        DatePicker::make('payment_date_until')->label(
                            __('Until Date'),
                        ),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['payment_date_from'],
                                fn (
                                    Builder $query,
                                    $date,
                                ): Builder => $query->whereDate(
                                    'payment_date',
                                    '>=',
                                    $date,
                                ),
                            )
                            ->when(
                                $data['payment_date_until'],
                                fn (
                                    Builder $query,
                                    $date,
                                ): Builder => $query->whereDate(
                                    'payment_date',
                                    '<=',
                                    $date,
                                ),
                            );
                    }),
            ])
            ->headerActions([
                CreateAction::make()
                    ->label(__('Add Receipt'))
                    ->schema([
                        Section::make(__('Receipt Information'))
                            ->schema([
                                Hidden::make('student_id')->default(
                                    fn () => $this->getOwnerRecord()->id,
                                ),

                                TextInput::make('receipt_number')
                                    ->label(__('Receipt Number'))
                                    ->required()
                                    ->unique(Receipt::class, 'receipt_number')
                                    ->default(
                                        fn () => Receipt::generateReceiptNumber(),
                                    ),

                                TextInput::make('payer_name')
                                    ->label(__('Payer Name'))
                                    ->required()
                                    ->maxLength(255)
                                    ->default(
                                        fn () => $this->getOwnerRecord()
                                            ->guardian?->name ?? '',
                                    )
                                    ->placeholder(
                                        __(
                                            'Enter the name of the person who made the payment',
                                        ),
                                    ),

                                TextInput::make('payment_amount')
                                    ->label(__('Payment Amount'))
                                    ->required()
                                    ->numeric()
                                    ->minValue(0.01)
                                    ->step(0.01)
                                    ->prefix(__('LYD'))
                                    ->placeholder('0.00'),

                                DatePicker::make('payment_date')
                                    ->label(__('Payment Date'))
                                    ->required()
                                    ->default(now())
                                    ->maxDate(now())
                                    ->displayFormat('Y-m-d'),

                                Textarea::make('notes')
                                    ->label(__('Notes'))
                                    ->placeholder(
                                        __(
                                            'Additional notes about this payment...',
                                        ),
                                    )
                                    ->rows(3)
                                    ->columnSpanFull(),
                            ])
                            ->columns(2),
                    ])
                    ->mutateDataUsing(function (array $data): array {
                        $data['student_id'] = $this->getOwnerRecord()->id;

                        return $data;
                    }),
            ])
            ->recordActions([
                EditAction::make()->schema([
                    Section::make(__('Receipt Information'))
                        ->schema([
                            TextInput::make('receipt_number')
                                ->label(__('Receipt Number'))
                                ->required()
                                ->unique(
                                    Receipt::class,
                                    'receipt_number',
                                    ignoreRecord: true,
                                )
                                ->disabled(),

                            TextInput::make('payer_name')
                                ->label(__('Payer Name'))
                                ->required()
                                ->maxLength(255),

                            TextInput::make('payment_amount')
                                ->label(__('Payment Amount'))
                                ->required()
                                ->numeric()
                                ->minValue(0.01)
                                ->step(0.01)
                                ->prefix('SAR'),

                            DatePicker::make('payment_date')
                                ->label(__('Payment Date'))
                                ->required()
                                ->maxDate(now())
                                ->displayFormat('Y-m-d'),

                            Textarea::make('notes')
                                ->label(__('Notes'))
                                ->rows(3)
                                ->columnSpanFull(),
                        ])
                        ->columns(2),
                ]),
                Action::make('print')
                    ->label(__('Print Receipt'))
                    ->icon('heroicon-o-printer')
                    ->color('success')
                    ->url(fn ($record) => route('receipts.print', $record))
                    ->openUrlInNewTab(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    \Filament\Actions\BulkAction::make('bulk_print')
                        ->label(__('Print Selected'))
                        ->icon('heroicon-o-printer')
                        ->color('success')
                        ->action(function ($records) {
                            $receiptIds = $records->pluck('id')->toArray();
                            return redirect()->route('receipts.bulk-print', ['receipts' => $receiptIds]);
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ])
            ->defaultSort('payment_date', 'desc')
            ->striped()
            ->paginated([10, 25, 50]);
    }
}
