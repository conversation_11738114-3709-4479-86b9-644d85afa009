<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Models\Classroom;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ClassroomAttendanceStats extends StatsOverviewWidget
{
    public Classroom $record;

    public string $date;

    public static function canView(): bool
    {
        return request()->routeIs('filament.admin.resources.classrooms.attendance');
    }

    protected function getStats(): array
    {
        $stats = $this->record->getAttendanceStatsForDate($this->date);

        return [
            Stat::make(__('Students'), $stats['total_students']),
            Stat::make(__('Present'), $stats['present_count'])->color('success'),
            Stat::make(__('Absent'), $stats['absent_count'])->color('danger'),
            Stat::make(__('Attendance %'), number_format($stats['attendance_percentage'], 0).'%')->color('primary'),
        ];
    }
}
