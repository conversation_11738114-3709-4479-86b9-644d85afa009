<?php

declare(strict_types=1);

if (!function_exists('convertAmountToWords')) {
    /**
     * Convert amount to words in Arabic or English, with currency names.
     * Assumes 2 decimal places = 1/100 of a dinar (dirhams).
     */
    function convertAmountToWords(float $amount, string $locale = 'en'): string
    {
        return $locale === 'ar'
            ? convertAmountToArabicWords($amount)
            : convertAmountToEnglishWords($amount);
    }
}

/* ===================== ENGLISH ===================== */

if (!function_exists('convertAmountToEnglishWords')) {
    function convertAmountToEnglishWords(float $amount): string
    {
        $integer = (int) floor($amount);
        $decimal = (int) round(($amount - $integer) * 100);

        $main = numberToEnglish($integer);
        $main .= ' ' . englishCurrencyWord($integer, 'dinar');

        if ($decimal > 0) {
            $main .=
                ' and ' .
                numberToEnglish($decimal) .
                ' ' .
                englishCurrencyWord($decimal, 'dirham');
        }

        return trim($main);
    }

    function numberToEnglish(int $n): string
    {
        if ($n === 0) {
            return 'Zero';
        }

        $ones = [
            '',
            'One',
            'Two',
            'Three',
            'Four',
            'Five',
            'Six',
            'Seven',
            'Eight',
            'Nine',
            'Ten',
            'Eleven',
            'Twelve',
            'Thirteen',
            'Fourteen',
            'Fifteen',
            'Sixteen',
            'Seventeen',
            'Eighteen',
            'Nineteen',
        ];
        $tens = [
            '',
            '',
            'Twenty',
            'Thirty',
            'Forty',
            'Fifty',
            'Sixty',
            'Seventy',
            'Eighty',
            'Ninety',
        ];
        $scales = [
            1000000000 => 'Billion',
            1000000 => 'Million',
            1000 => 'Thousand',
            100 => 'Hundred',
        ];

        $words = [];
        foreach ($scales as $value => $label) {
            if ($n >= $value) {
                $count = intdiv($n, $value);
                $n %= $value;
                $words[] = numberToEnglish($count) . ' ' . $label;
            }
        }

        if ($n >= 20) {
            $words[] =
                $tens[intdiv($n, 10)] . ($n % 10 ? '-' . $ones[$n % 10] : '');
        } elseif ($n > 0) {
            $words[] = $ones[$n];
        }

        return implode(' ', $words);
    }

    function englishCurrencyWord(int $n, string $unit): string
    {
        // Very light pluralization
        if ($unit === 'dinar') {
            return $n === 1 ? 'dinar' : 'dinars';
        }
        if ($unit === 'dirham') {
            return $n === 1 ? 'dirham' : 'dirhams';
        }
        return $unit;
    }
}

/* ===================== ARABIC ===================== */

if (!function_exists('convertAmountToArabicWords')) {
    function convertAmountToArabicWords(float $amount): string
    {
        $integer = (int) floor($amount);
        $decimal = (int) round(($amount - $integer) * 100);

        $mainNumber = numberToArabic($integer);
        $mainCurrency = arabicCurrencyWord($integer, 'dinar'); // accusative default

        $result = trim($mainNumber . ' ' . $mainCurrency);

        if ($decimal > 0) {
            $result .=
                ' و ' .
                numberToArabic($decimal) .
                ' ' .
                arabicCurrencyWord($decimal, 'dirham');
        }

        return $result;
    }

    /**
     * Arabic number words up to billions with correct composition:
     * 21 = "واحد وعشرون", 115 = "مائة وخمسة عشر", 2345 = "ألفان وثلاثمائة وخمسة وأربعون"
     */
    function numberToArabic(int $n): string
    {
        if ($n === 0) {
            return 'صفر';
        }

        $ones = [
            '',
            'واحد',
            'اثنان',
            'ثلاثة',
            'أربعة',
            'خمسة',
            'ستة',
            'سبعة',
            'ثمانية',
            'تسعة',
            'عشرة',
            'أحد عشر',
            'اثنا عشر',
            'ثلاثة عشر',
            'أربعة عشر',
            'خمسة عشر',
            'ستة عشر',
            'سبعة عشر',
            'ثمانية عشر',
            'تسعة عشر',
        ];

        $tens = [
            '',
            '',
            'عشرون',
            'ثلاثون',
            'أربعون',
            'خمسون',
            'ستون',
            'سبعون',
            'ثمانون',
            'تسعون',
        ];

        $hundredsForms = [
            0 => '',
            1 => 'مائة',
            2 => 'مائتان',
            3 => 'ثلاثمائة',
            4 => 'أربعمائة',
            5 => 'خمسمائة',
            6 => 'ستمائة',
            7 => 'سبعمائة',
            8 => 'ثمانمائة',
            9 => 'تسعمائة',
        ];

        $parts = [];

        $composeUnderThousand = function (int $x) use (
            $ones,
            $tens,
            $hundredsForms,
        ): string {
            $segments = [];

            // Hundreds
            $h = intdiv($x, 100);
            $r = $x % 100;
            if ($h > 0) {
                $segments[] = $hundredsForms[$h];
            }

            // Tens & Ones (Arabic order: ones then tens with "و")
            if ($r > 0) {
                if ($r < 20) {
                    $segments[] = $ones[$r];
                } else {
                    $unit = $r % 10;
                    $ten = intdiv($r, 10);
                    if ($unit === 0) {
                        $segments[] = $tens[$ten];
                    } else {
                        $segments[] = $ones[$unit] . ' و ' . $tens[$ten];
                    }
                }
            }

            // Join with "و"
            return implode(' و ', array_filter($segments));
        };

        // Scales
        $scales = [
            1000000000 => [
                'singular' => 'مليار',
                'dual' => 'ملياران',
                'plural' => 'مليارات',
            ],
            1000000 => [
                'singular' => 'مليون',
                'dual' => 'مليونان',
                'plural' => 'ملايين',
            ],
            1000 => [
                'singular' => 'ألف',
                'dual' => 'ألفان',
                'plural' => 'آلاف',
            ],
        ];

        foreach ($scales as $value => $forms) {
            if ($n >= $value) {
                $count = intdiv($n, $value);
                $n %= $value;

                if ($count === 1) {
                    $parts[] = $forms['singular'];
                } elseif ($count === 2) {
                    $parts[] = $forms['dual'];
                } elseif ($count <= 10) {
                    $parts[] = numberToArabic($count) . ' ' . $forms['plural'];
                } else {
                    // 11+ use singular scale word
                    $parts[] =
                        numberToArabic($count) . ' ' . $forms['singular'];
                }
            }
        }

        if ($n > 0) {
            $parts[] = $composeUnderThousand($n);
        }

        // Join with "و"
        return implode(' و ', array_filter($parts));
    }

    /**
     * Arabic currency word with simple grammatical agreement:
     * 0 -> "دينارًا", 1 -> "دينار واحد", 2 -> "ديناران", 3-10 -> "دنانير", 11+ -> "دينارًا"
     * Same pattern for درهم.
     */
    function arabicCurrencyWord(int $n, string $unit = 'dinar'): string
    {
        $isDinar = $unit === 'dinar';

        $one = $isDinar ? 'دينار واحد' : 'درهم واحد';
        $two = $isDinar ? 'ديناران' : 'درهمان';
        $plural = $isDinar ? 'دنانير' : 'دراهم';
        $accus = $isDinar ? 'دينارًا' : 'درهمًا';

        if ($n === 0) {
            return $accus;
        }
        if ($n === 1) {
            return $one;
        }
        if ($n === 2) {
            return $two;
        }
        if ($n >= 3 && $n <= 10) {
            return $plural;
        }

        // 11+ treated with singular accusative (تنوين النصب)
        return $accus;
    }
}
