<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Receipt;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;

class ReceiptPrintController extends Controller
{
    /**
     * Display a printable receipt
     */
    public function show(Receipt $receipt): View
    {
        // Load relationships for the receipt
        $receipt->load(['student.guardian', 'student.classroom']);

        return view('receipts.print', compact('receipt'));
    }

    /**
     * Generate PDF version of the receipt (optional)
     */
    public function pdf(Receipt $receipt): Response
    {
        // Load relationships for the receipt
        $receipt->load(['student.guardian', 'student.classroom']);

        // For now, return the HTML view
        // In the future, you can integrate with a PDF library like DomPDF or wkhtmltopdf
        $html = view('receipts.print', compact('receipt'))->render();

        return response($html)
            ->header('Content-Type', 'text/html')
            ->header(
                'Content-Disposition',
                'inline; filename="receipt-' .
                    $receipt->receipt_number .
                    '.html"',
            );
    }

    /**
     * Bulk print multiple receipts
     */
    public function bulk(Request $request, string $token)
    {
        // User-scoped cache key prevents cross-user access
        $userId = (string) $request->user()->getAuthIdentifier();
        $cacheKey = "bulk-print:{$userId}:{$token}";

        // Read (and invalidate) the selection
        $ids = Cache::pull($cacheKey);

        if (empty($ids) || !is_array($ids)) {
            // Token missing/expired/used
            abort(419);
        }

        $receipts = Receipt::whereIn('id', $ids)->get();

        if ($receipts->isEmpty()) {
            abort(404);
        }

        return view('receipts.bulk-print', compact('receipts'));
    }
}
