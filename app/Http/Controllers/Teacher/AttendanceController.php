<?php

declare(strict_types=1);

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Teacher\AttendanceRequest;
use App\Http\Resources\Teacher\AttendanceResource;
use App\Models\AttendanceSchedule;
use App\Models\Classroom;
use Exception;
use Illuminate\Http\Request;
use Inertia\Response;

class AttendanceController extends Controller
{
    /**
     * Display the attendance page for a specific classroom.
     *
     * @param  int  $classroomId
     * @return Response
     */
    public function index(Request $request, string $id)
    {
        $validator = validator(
            ['id' => $id, 'date' => $request->query('date')],
            [
                'id' => 'required|uuid|exists:classrooms,id',
                'date' => 'nullable|date_format:Y-m-d',
            ],
        );

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator->errors());
        }

        /** @var Teacher $user */
        $user = auth('teacher')->user();

        $validated = $validator->validated();
        $date = $validated['date'] ?? today();

        $classroom = $user
            ->classrooms()
            ->where('id', $validated['id'])
            ->with([
                'students.attendanceRecords' => function ($query) use ($date) {
                    if ($date) {
                        $query->whereDate('date', $date);
                    }
                },
            ])
            ->first();

        return inertia('teacher/attendance', [
            'classroom' => (new AttendanceResource($classroom))->resolve(),
            'backlink' => route('classroom', ['classroom' => $classroom->id]),
        ]);
    }

    public function store(Classroom $classroom, AttendanceRequest $request)
    {
        $validated = $request->validated();

        try {
            $user = auth('teacher')->user();

            AttendanceSchedule::updateOrCreate(
                [
                    'student_id' => $validated['student_id'],
                    'classroom_id' => $classroom->id,
                    'date' => $validated['date'],
                ],
                [
                    'type' => $validated['type'],
                    'operator_id' => $user->id,
                    'operator_type' => get_class($user),
                ],
            );

            return redirect()->back();
            // ->with('success', 'تم وضع علامة على المهمة كغير مكتملة');
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to record attendance.']);
        }
    }

    public function update(Classroom $classroom, AttendanceRequest $request)
    {
        $validated = $request->validated();

        try {
            $attendance = AttendanceSchedule::findOrFail(
                $validated['attendance_id'],
            );

            $attendance->update([
                'type' => $validated['type'],
                'notes' => $validated['notes'],
            ]);

            return redirect()->back();
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Attendance record not found.']);
        }
    }
}
