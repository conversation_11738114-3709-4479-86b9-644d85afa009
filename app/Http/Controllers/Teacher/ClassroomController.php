<?php

declare(strict_types=1);

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Resources\Teacher\ClassroomResource;
use App\Models\Teacher;
use Illuminate\Http\Request;

class ClassroomController extends Controller
{
    public function index(Request $request)
    {
        /** @var Teacher $user */
        $user = auth('teacher')->user();

        $dayName = now()->format('l'); // Make sure this matches DB format exactly

        $classrooms = $user
            ->classrooms()
            ->with('schedules') // remove where clause to test
            ->get();

        return inertia('teacher/classrooms', [
            'classrooms' => ClassroomResource::collection(
                $classrooms,
            )->resolve(),
            'dayName' => $dayName,
            'now' => now(),
        ]);
    }

    public function show(Request $request, string $id)
    {
        /** @var Teacher $user */
        $user = auth('teacher')->user();

        $validator = validator(
            ['id' => $id],
            [
                'id' => 'required|uuid|exists:classrooms,id',
            ],
        );

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator->errors());
        }

        $classroom = $user->classrooms()->findOrFail($id);

        return inertia('teacher/classroom', [
            'classroom' => new ClassroomResource($classroom),
        ]);
    }
}
