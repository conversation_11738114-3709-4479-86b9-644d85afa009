<?php

declare(strict_types=1);

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Classroom;
use App\Models\Question;
use Illuminate\Http\Request;

class QuestionsController extends Controller
{
    public function index(Request $request, Classroom $classroom)
    {
        $teacher = $request->user();
        $questions = $teacher
            ->questions()
            ->with(['verse', 'startVerse', 'endVerse'])
            ->get();

        return inertia('teacher/questions/index', [
            'backlink' => route('classroom', ['classroom' => $classroom]),
            'classroom' => $classroom,
            'questions' => $questions,
        ]);
    }

    public function create(Request $request, Classroom $classroom)
    {
        return inertia('teacher/questions/create', [
            'backlink' => route('questions', ['classroom' => $classroom]),
            'classroom' => $classroom,
        ]);
    }

    public function store(Request $request, Classroom $classroom)
    {
        $validated = $request->validate([
            'verse_id' => [
                'nullable',
                'exists:verses,id',
                'required_without_all:start_verse_id,end_verse_id',
            ],
            'start_verse_id' => [
                'nullable',
                'exists:verses,id',
                'required_with:end_verse_id',
                'prohibits:verse_id',
            ],
            'end_verse_id' => [
                'nullable',
                'exists:verses,id',
                'required_with:start_verse_id',
                'prohibits:verse_id',
            ],
        ]);

        $data = [
            'type' => 'personal',
            'status' => 'pending',
        ];

        if (isset($validated['verse_id'])) {
            $data['verse_id'] = $validated['verse_id'];
        } else {
            $data['start_verse_id'] = $validated['start_verse_id'];
            $data['end_verse_id'] = $validated['end_verse_id'];
        }

        $classroom->teacher->questions()->create($data);

        return redirect()->route('questions', ['classroom' => $classroom]);
    }

    public function update(
        Request $request,
        Classroom $classroom,
        Question $question,
    ) {
        $validated = $request->validate([
            'start_verse_id' => ['sometimes', 'required', 'exists:verses,id'],
            'end_verse_id' => ['sometimes', 'required', 'exists:verses,id'],
        ]);

        $question->update($validated);

        return redirect()->route('questions', ['classroom' => $classroom]);
    }

    public function edit(
        Request $request,
        Classroom $classroom,
        Question $question,
    ) {
        $question->load(['startVerse', 'endVerse']);

        return inertia('teacher/questions/edit', [
            'backlink' => route('questions', ['classroom' => $classroom]),
            'classroom' => $classroom,
            'question' => $question,
        ]);
    }

    public function destroy(Request $request, Question $question)
    {
        $question->delete();

        return back();
    }
}
