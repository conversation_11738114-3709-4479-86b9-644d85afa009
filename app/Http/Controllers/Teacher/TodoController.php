<?php

declare(strict_types=1);

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Resources\Teacher\TodoResource;
use App\Models\Classroom;
use App\Models\TODO;
use Illuminate\Http\Request;

class TodoController extends Controller
{
    public function index(Request $request, Classroom $classroom)
    {
        // <PERSON><PERSON> resolves by UUID
        $teacherId = auth('teacher')->id();

        $query = TODO::where('classroom_id', $classroom->id)
            ->where('teacher_id', $teacherId);

        if ($request->input('filter') === 'today') {
            $query->whereDate('created_at', today());
        }

        $todos = $query->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'todos' => TodoResource::collection($todos)->resolve(),
            'meta' => [
                'current_page' => $todos->currentPage(),
                'next_page_url' => $todos->nextPageUrl(),
            ],
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => ['nullable', 'string', 'max:255'],
            'body' => ['required', 'string'],
            'classroom_id' => ['required', 'uuid', 'exists:classrooms,id'],
        ]);

        $validated['teacher_id'] = auth('teacher')->user()->id;

        $todo = TODO::create([
            'title' => $validated['title'],
            'body' => $validated['body'],
            'classroom_id' => $validated['classroom_id'],
            'teacher_id' => $validated['teacher_id'],
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'todo' => (new TodoResource($todo))->resolve(),
            ]);
        }

        return redirect()->back()->with('success', 'تمت إضافة المهمة بنجاح');
    }

    public function rollback(Request $request, TODO $todo)
    {
        $todo->update(['is_done' => false]);

        if ($request->expectsJson()) {
            return response()->json([
                'todo' => (new TodoResource($todo))->resolve(),
            ]);
        }

        return redirect()
            ->back()
            ->with('success', 'تم وضع علامة على المهمة كغير مكتملة');
    }

    public function done(TODO $todo)
    {
        $todo->update(['is_done' => true]);

        return response()->json([
            'todo' => (new TodoResource($todo))->resolve(),
        ]);
    }

    public function destroy(TODO $todo)
    {
        $todo->delete();

        if (request()->expectsJson()) {
            return response()->json([
                'message' => 'تم حذف المهمة',
                'success' => true,
            ]);
        }

        return redirect()->back()->with('success', 'تم حذف المهمة');
    }

    public function restore(string $id)
    {
        $todo = TODO::withTrashed()->findOrFail($id);

        // Ensure the todo belongs to the authenticated teacher
        if ($todo->teacher_id !== auth('teacher')->id()) {
            abort(403, 'Unauthorized');
        }

        $todo->restore();

        return response()->json([
            'todo' => (new TodoResource($todo))->resolve(),
        ]);
    }
}
