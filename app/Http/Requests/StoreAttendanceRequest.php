<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAttendanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'student_id' => [
                'required',
                'uuid',
                'exists:students,id',
                Rule::unique('attendance_schedules')
                    ->where('classroom_id', $this->classroom_id)
                    ->where('date', $this->date)
                    ->ignore($this->route('attendance_schedule')),
            ],
            'classroom_id' => [
                'required',
                'uuid',
                'exists:classrooms,id',
            ],
            'type' => [
                'required',
                'in:Present,Absent',
            ],
            'date' => [
                'required',
                'date',
            ],
            'operator_id' => [
                'required',
                'uuid',
            ],
            'operator_type' => [
                'required',
                'string',
                'in:App\Models\User,App\Models\Teacher',
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'student_id.unique' => 'An attendance record for this student on this date already exists for this classroom.',
            'type.in' => 'Attendance type must be either Present or Absent.',
            'operator_type.in' => 'Operator must be either a User or Teacher.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure operator_type is properly formatted
        if ($this->has('operator_type') && ! str_starts_with($this->operator_type, 'App\\Models\\')) {
            $this->merge([
                'operator_type' => 'App\\Models\\'.ucfirst($this->operator_type),
            ]);
        }
    }
}
