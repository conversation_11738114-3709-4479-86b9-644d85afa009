<?php

declare(strict_types=1);

namespace App\Http\Requests\Teacher;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AttendanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'student_id' => ['required', 'exists:students,id'],
            'type' => ['required', 'string', 'in:Present,Absent'],
            'date' => ['required'],
            'notes' => ['nullable', 'string'],
            'attendance_id' => ['nullable', 'exists:attendance_schedules,id'],
        ];
    }
}
