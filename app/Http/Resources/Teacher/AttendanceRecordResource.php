<?php

declare(strict_types=1);

namespace App\Http\Resources\Teacher;

use App\Models\AttendanceSchedule;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttendanceRecordResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var AttendanceSchedule $attendanceRecord */
        $attendanceRecord = $this->resource;

        return [
            'id' => $attendanceRecord->id,
            'date' => $attendanceRecord->date->format('Y-m-d'),
            'type' => $attendanceRecord->type,
            'notes' => $attendanceRecord->notes,
        ];
    }
}
