<?php

declare(strict_types=1);

namespace App\Http\Resources\Teacher;

use App\Models\Classroom;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttendanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Classroom $classroom */
        $classroom = $this->resource;

        return [
            'id' => $classroom->id,
            'name' => $classroom->name,
            'students' => StudentResource::collection(
                $classroom->students,
            )->resolve(),
        ];
    }
}
