<?php

declare(strict_types=1);

namespace App\Http\Resources\Teacher;

use App\Models\Classroom;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClassroomResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Classroom $classroom */
        $classroom = $this->resource;

        $dayName = strtolower(now()->format('l'));

        return [
            'id' => $classroom->id,
            'name' => $classroom->name,
            'description' => $classroom->description,
            'capacity' => $classroom->capacity,
            'is_active' => $classroom->is_active,
            'room_number' => $classroom->room_number,
            'schedules' => ClassroomSchedule::collection(
                $classroom->schedules
                    ->filter(fn ($schedule) => $schedule->weekday === $dayName)
                    ->values(),
            )->resolve(),
        ];
    }
}
