<?php

declare(strict_types=1);

namespace App\Http\Resources\Teacher;

use App\Models\ClassroomSchedule as Schedule;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClassroomSchedule extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Schedule $schedule */
        $schedule = $this->resource;

        return [
            'id' => $schedule->id,
            'weekday' => $schedule->weekday,
            'start_time' => $schedule->start_time->format('H:i'),
            'end_time' => $schedule->end_time->format('H:i'),
            'is_active' => $schedule->is_active,
        ];
    }
}
