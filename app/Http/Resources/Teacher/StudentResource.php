<?php

declare(strict_types=1);

namespace App\Http\Resources\Teacher;

use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Student $student */
        $student = $this->resource;

        $studentAttendance = $student->attendanceRecords;

        return [
            'id' => $student->id,
            'name' => $student->name,
            'registration_number' => $student->registration_number,
            'attendance' => AttendanceRecordResource::collection(
                $studentAttendance,
            )->resolve(),
        ];
    }
}
