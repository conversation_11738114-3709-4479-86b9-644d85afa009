<?php

declare(strict_types=1);

namespace App\Http\Resources\Teacher;

use App\Models\TODO;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TodoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var TODO $todo */
        $todo = $this->resource;

        return [
            'id' => $todo->id,
            'title' => $todo->title,
            'body' => $todo->body,
            'created_at' => $todo->created_at->diffForHumans(),
            'created_date' => $todo->created_at->toDateString(),
            'is_done' => $todo->is_done,
        ];
    }
}
