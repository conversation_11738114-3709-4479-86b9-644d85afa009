<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Actions\Sms\CheckMessageStatus;
use App\Enum\MessageStatus;
use App\Models\SmsMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CheckSmsMessageStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public SmsMessage $sms) {}

    public function handle(): void
    {
        if (! $this->sms->message_id) {
            return;
        }

        $action = new CheckMessageStatus(config('sms.api_key'));
        $response = $action($this->sms->message_id);

        Log::info('SMS status check response', ['response' => $response]);

        if (! $response['success']) {
            return;
        }

        $messages = $response['response'] ?? [];

        foreach ($messages as $message) {
            $receiver = $message['receiver'][0] ?? null;

            if (! $receiver || ! isset($receiver['status'])) {
                continue;
            }

            if ($receiver['number'] === $this->sms->receiver) {
                $deliveryStatus = $receiver['status'];

                $update = [
                    'delivery_status' => $deliveryStatus,
                    'delivered_at' => $receiver['delivered_at'] ?? null,
                ];

                if (strtolower($deliveryStatus) === 'delivered') {
                    $update['status'] = MessageStatus::Delivered->value;
                }

                $this->sms->update($update);
                break;
            }
        }
    }
}
