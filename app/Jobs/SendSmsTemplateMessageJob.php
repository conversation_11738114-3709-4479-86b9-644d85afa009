<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Actions\Sms\SendTemplateSms;
use App\Enum\MessageStatus;
use App\Models\SmsMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendSmsTemplateMessageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public string $smsId;

    public function __construct(string $smsId)
    {
        $this->smsId = $smsId;
    }

    public function handle(): void
    {
        $sms = SmsMessage::whereKey($this->smsId)
            ->where('status', MessageStatus::Pending->value)
            ->first();

        if (! $sms) {
            return;
        }

        $action = new SendTemplateSms(
            authToken: config('sms.api_key'),
            defaultSenderId: $sms->sender,
            defaultPaymentType: $sms->payment_type,
        );

        $parameters = $this->convertParams($sms->params ?? []);

        $response = $action([
            'template_id' => $sms->template_id,
            'sender' => $sms->sender,
            'payment_type' => $sms->payment_type,
            'receiver' => $sms->receiver,
            'params' => $parameters,
        ]);

        $update = [
            'api_response' => $response['response'],
            'status' => $response['success'] ? MessageStatus::Sent->value : MessageStatus::Failed->value,
        ];

        if ($response['success']) {
            $update['message_id'] = data_get($response['response'], 'message_id')
                ?? data_get($response['response'], 'id');
        }

        $sms->update($update);
    }

    protected function convertParams(array $params): array
    {
        // If already in the correct format: [{key => value}, ...], keep it
        if (isset($params[0]) && is_array($params[0])) {
            return $params;
        }

        // Convert from ['key' => 'value', ...] to [{key => value}, ...]
        return collect($params)
            ->map(fn ($value, $key) => [$key => $value])
            ->values()
            ->toArray();
    }
}
