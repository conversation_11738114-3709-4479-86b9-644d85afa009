<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\Chapter;
use App\Models\Verse;
use Illuminate\View\View;
use Livewire\Attributes\Url;
use Livewire\Component;

class QuranViewer extends Component
{
    public int $loaded = 30;

    #[Url]
    public $selectedChapter = null;

    public $chapters;

    #[Url]
    public $search = '';

    public function mount(): void
    {
        $this->chapters = Chapter::all()->sortBy('number'); // Use 'number' as the column for sorting chapters
    }

    public function loadMore(): void
    {
        $this->loaded += 30;
    }

    public function render(): View
    {
        $query = Verse::with('chapter');

        if ($this->search) {
            $query->whereTextStartsWith($this->search);
        }

        if ($this->selectedChapter) {
            $query->where('chapter_id', $this->selectedChapter);
        }

        // Join with chapters table to order by chapters.number (the correct column)
        $verses = $query->join('chapters', 'verses.chapter_id', '=', 'chapters.id')
            ->orderBy('chapters.number') // Order by the 'number' column in the chapter table
            ->orderBy('verses.number')   // Then by verse number within that chapter
            ->select('verses.*')         // Select verses.* to avoid conflicts with chapter columns
            ->take($this->loaded)
            ->get();

        // Group by chapter_id and page (this grouping remains useful for display)

        $grouped = $verses->groupBy(function ($verse) {
            return $verse->chapter_id.'{-}'.$verse->page;
        });

        return view('livewire.quran-viewer', [
            'grouped' => $grouped,
            'chapters' => $this->chapters,
        ]);
    }
}
