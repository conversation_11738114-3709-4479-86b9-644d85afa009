<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Exam extends Model
{
    use HasUuids;

    protected $fillable = ['name', 'teacher_id', 'classroom_id', 'exam_setting_profile_id'];

    public function students(): HasMany
    {
        return $this->hasMany(ExamStudent::class);
    }

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    public function classroom(): BelongsTo
    {
        return $this->belongsTo(Classroom::class);
    }

    public function examSettingProfile(): BelongsTo
    {
        return $this->belongsTo(ExamSettingProfile::class);
    }

    public function studentQuestions()
    {
        return $this->hasManyThrough(
            ExamStudentQuestion::class,
            ExamStudent::class,
            'exam_id', // Foreign key on ExamStudent table
            'student_id', // Local key on ExamStudentQuestion (matches ExamStudent::student_id)
            'id', // Local key on Exam
            'student_id', // Foreign key on ExamStudent
        );
    }
}
