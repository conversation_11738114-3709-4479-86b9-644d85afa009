<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ExamAspect extends Model
{
    use HasUuids;

    protected $fillable = [
        'profile_id',
        'key',
        'label',
        'weight',
        'fail_aspect_key',
        'sort',
        'has_sub_aspects',
    ];

    public function profile(): BelongsTo
    {
        return $this->belongsTo(ExamSettingProfile::class);
    }
    public function subAspects(): HasMany
    {
        return $this->hasMany(ExamSubAspect::class)->orderBy('sort');
    }

    public function isGrouped(): bool
    {
        return (bool) $this->has_sub_aspects;
    }
}
