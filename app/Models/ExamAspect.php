<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ExamAspect extends Model
{
    use HasUuids;

    protected $fillable = [
        'profile_id',
        'key',
        'label',
        'weight',
        'fail_aspect_key',
        'sort',
        'has_sub_aspects',
        'deduction_per_mistake',
        'fail_threshold',
    ];

    public function profile(): BelongsTo
    {
        return $this->belongsTo(ExamSettingProfile::class, 'profile_id');
    }
    public function subAspects(): HasMany
    {
        return $this->hasMany(ExamSubAspect::class, 'aspect_id')->orderBy(
            'sort',
        );
    }

    public function isGrouped(): bool
    {
        return (bool) $this->has_sub_aspects;
    }
}
