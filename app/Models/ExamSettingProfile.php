<?php

namespace App\Models;

use App\Observers\ExamSettingProfileObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

#[ObservedBy([ExamSettingProfileObserver::class])]
class ExamSettingProfile extends Model
{
    use HasUuids;

    protected $fillable = ['name', 'description', 'is_active'];

    public function aspects(): HasMany
    {
        return $this->hasMany(ExamAspect::class, 'profile_id')->orderBy('sort');
    }

    public function scopeActive()
    {
        return $this->where('is_active', true);
    }

    /**
     * Get the currently active exam setting profile
     */
    public static function getActive(): ?self
    {
        return static::where('is_active', true)->first();
    }

    /**
     * Get all aspects and sub-aspects in a flat structure for easy lookup
     */
    public function getAllAspectsFlat(): array
    {
        $flatAspects = [];

        foreach ($this->aspects as $aspect) {
            if ($aspect->has_sub_aspects) {
                // For grouped aspects, add each sub-aspect
                foreach ($aspect->subAspects as $subAspect) {
                    $flatAspects[$subAspect->key] = [
                        'type' => 'sub_aspect',
                        'key' => $subAspect->key,
                        'label' => $subAspect->label,
                        'weight' => $aspect->weight, // Sub-aspects inherit parent weight
                        'deduction_per_mistake' => $subAspect->deduction_per_mistake,
                        'fail_threshold' => $subAspect->fail_threshold,
                        'parent_aspect' => $aspect,
                        'aspect_key' => $aspect->key,
                        'fail_aspect_key' => $aspect->fail_aspect_key,
                    ];
                }
            } else {
                // For flat aspects, add the aspect itself
                $flatAspects[$aspect->key] = [
                    'type' => 'aspect',
                    'key' => $aspect->key,
                    'label' => $aspect->label,
                    'weight' => $aspect->weight,
                    'deduction_per_mistake' => $aspect->deduction_per_mistake,
                    'fail_threshold' => $aspect->fail_threshold,
                    'parent_aspect' => null,
                    'aspect_key' => $aspect->key,
                    'fail_aspect_key' => null,
                ];
            }
        }

        return $flatAspects;
    }

    /**
     * Get scoring configuration for a specific aspect/sub-aspect key
     */
    public function getScoringConfig(string $key): ?array
    {
        $flatAspects = $this->getAllAspectsFlat();
        return $flatAspects[$key] ?? null;
    }

    /**
     * Check if an aspect/sub-aspect should cause failure based on mistakes
     */
    public function shouldFail(string $key, int $mistakes): bool
    {
        $config = $this->getScoringConfig($key);

        if (!$config) {
            return false;
        }

        // Check direct fail threshold
        if ($config['fail_threshold'] > 0 && $mistakes >= $config['fail_threshold']) {
            return true;
        }

        // Check if this is the designated fail aspect for grouped aspects
        if ($config['type'] === 'sub_aspect' && $config['fail_aspect_key'] === $key) {
            return $mistakes > 0; // Any mistake in fail aspect causes failure
        }

        return false;
    }
}
