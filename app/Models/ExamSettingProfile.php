<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ExamSettingProfile extends Model
{
    use HasUuids;

    protected $fillable = ['name', 'description', 'is_active'];

    public function aspects(): HasMany
    {
        return $this->hasMany(ExamAspect::class)->orderBy('sort');
    }

    public function scopeActive()
    {
        return $this->where('is_active', true);
    }
}
