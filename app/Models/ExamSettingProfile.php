<?php

namespace App\Models;

use App\Observers\ExamSettingProfileObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

#[ObservedBy([ExamSettingProfileObserver::class])]
class ExamSettingProfile extends Model
{
    use HasUuids;

    protected $fillable = ['name', 'description', 'is_active'];

    public function aspects(): HasMany
    {
        return $this->hasMany(ExamAspect::class, 'profile_id')->orderBy('sort');
    }
}
