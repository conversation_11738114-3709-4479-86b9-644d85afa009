<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ExamStudent extends Model
{
    use HasUuids;

    protected $table = 'exam_students';

    protected $fillable = [
        'exam_id',
        'student_id',
        'is_started',
        'started_at',
        'completed_at',
    ];

    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function questions(): HasMany
    {
        return $this->hasMany(
            ExamStudentQuestion::class,
            'student_id',
            'student_id',
        );
    }

    public function getFilteredQuestionsAttribute()
    {
        return $this->questions->where('exam_id', $this->exam_id);
    }

    public function getTotalScoreAttribute(): float
    {
        return $this->questions->flatMap->ratings // flatten all ratings
            ->sum('score');
    }

    public function loadWithQuestions(): void
    {
        $this->load([
            'questions.question.verse.chapter',
            'questions.question.startVerse.chapter',
            'questions.question.endVerse.chapter',
            'questions.ratings',
        ]);
    }
}
