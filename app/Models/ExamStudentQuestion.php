<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ExamStudentQuestion extends Model
{
    use HasUuids;

    protected $fillable = ['exam_id', 'student_id', 'question_id'];

    protected $appends = ['rating_summary'];

    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class);
    }

    public function ratings(): HasMany
    {
        return $this->hasMany(ExamStudentQuestionRating::class);
    }

    public function calculateTotalRating(): array
    {
        $totalScore = $this->ratings->sum('score');
        $totalPossible = $this->ratings->sum('weight');

        return [
            'total_score' => $totalScore,
            'total_possible' => $totalPossible,
            'percentage' => $totalPossible > 0
                    ? round(($totalScore / $totalPossible) * 100, 2)
                    : 0,
        ];
    }

    public function getRatingSummaryAttribute(): array
    {
        return $this->calculateTotalRating();
    }
}
