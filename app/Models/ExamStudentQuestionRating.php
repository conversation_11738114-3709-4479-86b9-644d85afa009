<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExamStudentQuestionRating extends Model
{
    use HasUuids;

    protected $fillable = [
        'exam_student_question_id',
        'aspect',
        'mistakes',
        'score',
        'weight',
        'deduction_per_mistake',
    ];

    public function questionAssignment(): BelongsTo
    {
        return $this->belongsTo(
            ExamStudentQuestion::class,
            'exam_student_question_id',
        );
    }

    protected static function booted(): void
    {
        static::saving(function (self $rating) {
            // Get the exam and its setting profile
            $question = $rating->questionAssignment;
            if (!$question || !$question->exam) {
                $rating->score = 0;
                logger()->error('Rating calculation failed: Question or exam not found.');
                return;
            }

            $exam = $question->exam;
            $profile = $exam->examSettingProfile;

            if (!$profile) {
                // Fallback to active profile if exam doesn't have one assigned
                $profile = ExamSettingProfile::getActive();
            }

            if (!$profile) {
                $rating->score = 0;
                logger()->error('Rating calculation failed: No exam setting profile found.');
                return;
            }

            // Get scoring configuration for this aspect
            $config = $profile->getScoringConfig($rating->aspect);

            if (!$config) {
                $rating->score = 0;
                logger()->error('Rating calculation failed: Aspect configuration not found.', [
                    'aspect' => $rating->aspect,
                    'profile_id' => $profile->id,
                ]);
                return;
            }

            $weight = (float) $config['weight'];
            $deduction = (float) $config['deduction_per_mistake'];
            $mistakes = (int) $rating->mistakes;
            $fail_threshold = (int) $config['fail_threshold'];

            $rating->weight = $weight;
            $rating->deduction_per_mistake = $deduction;
            $rating->fail_threshold = $fail_threshold;

            // Check if this should cause failure
            if ($profile->shouldFail($rating->aspect, $mistakes)) {
                $rating->score = 0;
                logger()->debug('Fail threshold met', [
                    'aspect' => $rating->aspect,
                    'mistakes' => $mistakes,
                    'fail_threshold' => $fail_threshold,
                    'final_score' => 0,
                ]);
            } else {
                $calculatedScore = $weight - $mistakes * $deduction;
                $finalScore = max(0, $calculatedScore);
                $rating->score = round($finalScore, 2);
            }
        });

        static::saved(function (self $rating) {
            $question = $rating->questionAssignment;

            if (! $question) {
                return;
            }

            $examId = $question->exam_id;
            $studentId = $question->student_id;

            $examStudent = ExamStudent::where('exam_id', $examId)
                ->where('student_id', $studentId)
                ->first();

            if (! $examStudent) {
                return;
            }

            // ✅ 1. Mark exam as started
            if (! $examStudent->is_started) {
                $examStudent->is_started = true;
                $examStudent->started_at = now();
            }

            // ✅ 2. Check if all questions are rated
            $allRated = $examStudent
                ->questions()
                ->with('ratings')
                ->get()
                ->every(fn ($q) => $q->ratings->isNotEmpty());

            if ($allRated && is_null($examStudent->completed_at)) {
                $examStudent->completed_at = now();
            }

            // ✅ Save only if anything changed
            if (
                $examStudent->isDirty([
                    'is_started',
                    'started_at',
                    'completed_at',
                ])
            ) {
                $examStudent->save();
            }
        });
    }
}
