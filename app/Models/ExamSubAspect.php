<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExamSubAspect extends Model
{
    use HasUuids;

    protected $fillable = [
        'aspect_id',
        'key',
        'label',
        'deduction_per_mistake',
        'fail_threshold',
        'sort',
    ];

    public function aspect(): BelongsTo
    {
        return $this->belongsTo(ExamAspect::class, 'aspect_id');
    }
}
