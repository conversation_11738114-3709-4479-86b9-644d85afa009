<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Guardian extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'national_id',
        'emergency_contact',
        'relationship_to_student',
        'notes',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * Get all students for this guardian
     */
    public function students(): HasMany
    {
        return $this->hasMany(Student::class);
    }

    /**
     * Get active students for this guardian
     */
    public function activeStudents(): HasMany
    {
        return $this->hasMany(Student::class)->where('is_active', true);
    }

    /**
     * Get the full name with the title
     */
    public function getFullNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Get students count
     */
    public function getStudentsCountAttribute(): int
    {
        return $this->students()->count();
    }
}
