<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Question extends Model
{
    use HasUuids;

    protected $fillable = [
        'teacher_id',
        'verse_id',
        'start_verse_id',
        'end_verse_id',
        'status',
        'type',
    ];

    public function teacher(): BelongsT<PERSON>
    {
        return $this->belongsTo(Teacher::class);
    }

    public function verse(): BelongsTo
    {
        return $this->belongsTo(Verse::class);
    }

    public function startVerse(): BelongsTo
    {
        return $this->belongsTo(Verse::class, 'start_verse_id');
    }

    public function endVerse(): BelongsTo
    {
        return $this->belongsTo(Verse::class, 'end_verse_id');
    }

    public function getTitleAttribute(): string
    {
        $question = $this;

        if ($question?->verse) {
            return trans('Verse').
                ': '.
                $question->verse->number.
                ' '.
                trans('Chapter').
                ' - '.
                $question->verse->chapter->name;
        }

        if ($question?->startVerse && $question?->endVerse) {
            return trans('From Ayah').
                ' '.
                $question->startVerse->number.
                ' '.
                trans('Chapter').
                ' '.
                $question->startVerse->chapter->name.
                ' '.
                trans('to Ayah').
                ' '.
                $question->endVerse->number.
                ' '.
                trans('Chapter').
                ' '.
                $question->endVerse->chapter->name;
        }

        return 'Unnamed Question ('.Str::limit($question?->id, 8).')';
    }
}
