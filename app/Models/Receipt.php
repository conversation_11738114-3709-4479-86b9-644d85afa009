<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Receipt extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'student_id',
        'receipt_number',
        'payer_name',
        'payment_amount',
        'payment_date',
        'notes',
    ];

    protected $casts = [
        'payment_date' => 'date',
        'payment_amount' => 'decimal:2',
    ];

    /**
     * Generate a unique receipt number
     */
    public static function generateReceiptNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');

        // Get the last receipt number for this month
        $lastReceipt = static::whereYear('payment_date', $year)
            ->whereMonth('payment_date', $month)
            ->orderBy('receipt_number', 'desc')
            ->first();

        if ($lastReceipt) {
            // Extract the sequence number from the last receipt
            $lastNumber = (int) substr($lastReceipt->receipt_number, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return sprintf('RCP-%s%s-%04d', $year, $month, $nextNumber);
    }

    /**
     * Get the student that owns the receipt
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get formatted payment amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->payment_amount, 2).' SAR';
    }
}
