<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentProgression extends Model
{
    use HasUuids;

    protected $fillable = [
        'verse_id',
        'student_id',
        'status',
    ];

    /** @return BelongsTo<Verse, $this> */
    public function verse(): BelongsTo
    {
        return $this->belongsTo(Verse::class);
    }

    /** @return BelongsTo<Student, $this> */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }
}
