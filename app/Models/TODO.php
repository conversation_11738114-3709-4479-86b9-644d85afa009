<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class TODO extends Model
{
    use HasUuids;
    use SoftDeletes;

    protected $table = 'todos';

    protected $fillable = [
        'title',
        'body',
        'is_done',
        'classroom_id',
        'teacher_id',
    ];

    protected $casts = [
        'is_done' => 'boolean',
    ];

    public function classroom(): BelongsTo
    {
        return $this->belongsTo(Classroom::class);
    }

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }
}
