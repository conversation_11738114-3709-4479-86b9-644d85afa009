<?php

namespace App\Observers;

use App\Models\ExamSettingProfile;

class ExamSettingProfileObserver
{
    /**
     * Handle the ExamSettingProfile "created" event.
     */
    public function created(ExamSettingProfile $examSettingProfile): void
    {
        if ($examSettingProfile->is_active) {
            $this->deactivateOtherProfiles($examSettingProfile);
        }
    }

    /**
     * Handle the ExamSettingProfile "updated" event.
     */
    public function updated(ExamSettingProfile $examSettingProfile): void
    {
        if ($examSettingProfile->is_active && $examSettingProfile->wasChanged('is_active')) {
            $this->deactivateOtherProfiles($examSettingProfile);
        }
    }

    /**
     * Deactivate all other profiles when one is activated
     */
    private function deactivateOtherProfiles(ExamSettingProfile $activeProfile): void
    {
        ExamSettingProfile::where('id', '!=', $activeProfile->id)
            ->where('is_active', true)
            ->update(['is_active' => false]);
    }

    /**
     * Handle the ExamSettingProfile "deleted" event.
     */
    public function deleted(ExamSettingProfile $examSettingProfile): void
    {
        //
    }

    /**
     * Handle the ExamSettingProfile "restored" event.
     */
    public function restored(ExamSettingProfile $examSettingProfile): void
    {
        //
    }

    /**
     * Handle the ExamSettingProfile "force deleted" event.
     */
    public function forceDeleted(ExamSettingProfile $examSettingProfile): void
    {
        //
    }
}
