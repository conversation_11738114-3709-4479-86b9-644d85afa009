<?php

declare(strict_types=1);

namespace App\Observers;

use App\Models\Student;

class StudentObserver
{
    public function saving(Student $student): void
    {
        if ($student->classroom_id && empty($student->registration_number)) {
            $student->registration_number = $student->generateRegistrationNumber();
        }
    }

    public function updated(Student $student): void
    {
        if ($student->classroom_id && empty($student->registration_number)) {
            Student::query()->whereKey($student->getKey())->update([
                'registration_number' => $student->generateRegistrationNumber(),
            ]);
        }
    }
}
