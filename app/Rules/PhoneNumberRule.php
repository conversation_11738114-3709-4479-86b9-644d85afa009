<?php

declare(strict_types=1);

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Translation\PotentiallyTranslatedString;

class PhoneNumberRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  Closure(string): PotentiallyTranslatedString  $fail
     */
    public function validate(
        string $attribute,
        mixed $value,
        Closure $fail,
    ): void {
        if (! is_string($value)) {
            $fail('The :attribute must be a string.');

            return;
        }
        if (! $this->passes($value)) {
            $fail($this->message());
        }
    }

    /**
     * Determine if the validation rule passes.
     */
    private function passes(string $value): bool
    {
        $providers = [
            '^(?:00218)?(?:92|94|95)\d{7}$', // 92, 94, 95
            '^(?:00218)?(?:91|93)\d{7}$',    // 91, 93
        ];

        foreach ($providers as $provider) {
            if (preg_match("/{$provider}/", $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the validation error message.
     */
    private function message(): string
    {
        return __('The :attribute must be valid in system providers.');
    }
}
