<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Receipt;
use App\Models\Student;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Receipt>
 */
class ReceiptFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'student_id' => Student::factory(),
            'receipt_number' => Receipt::generateReceiptNumber(),
            'payer_name' => $this->faker->name(),
            'payment_amount' => $this->faker->randomFloat(2, 50, 500),
            'payment_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'notes' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the receipt is for a specific month.
     */
    public function forMonth(int $year, int $month): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_date' => $this->faker->dateTimeBetween(
                "{$year}-{$month}-01",
                "{$year}-{$month}-".date('t', mktime(0, 0, 0, $month, 1, $year))
            ),
        ]);
    }

    /**
     * Indicate that the receipt is for today.
     */
    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_date' => now()->toDateString(),
        ]);
    }

    /**
     * Indicate that the receipt has a specific amount.
     */
    public function amount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_amount' => $amount,
        ]);
    }
}
