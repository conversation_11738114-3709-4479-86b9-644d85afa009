<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('verses', function (Blueprint $table) {
            $table->integer('eighth')->nullable()->after('line_end');
        });
    }

    public function down(): void
    {
        Schema::table('verses', function (Blueprint $table) {
            //
        });
    }
};
