<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('teacher_id')->nullable()->constrained('teachers')->cascadeOnDelete();
            $table->foreignUuid('verse_id')->nullable()->constrained('verses')->cascadeOnDelete();
            $table->foreignUuid('start_verse_id')->nullable()->constrained('verses')->cascadeOnDelete();
            $table->foreignUuid('end_verse_id')->nullable()->constrained('verses')->cascadeOnDelete();
            $table->string('status');
            $table->string('type');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
