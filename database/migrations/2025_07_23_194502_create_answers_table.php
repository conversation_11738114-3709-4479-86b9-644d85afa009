<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('answers', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->foreignUuid('question_id')
                ->constrained('questions')
                ->cascadeOnDelete();

            $table->foreignUuid('student_id')
                ->constrained('students')
                ->cascadeOnDelete();

            $table->unsignedTinyInteger('memorization'); // الحفظ
            $table->unsignedTinyInteger('tajweed_rules'); // الأحكام
            $table->unsignedTinyInteger('narration_principles'); // أصل الرواية
            $table->unsignedTinyInteger('stopping_starting_rules'); // الوقف والبداية
            $table->unsignedTinyInteger('recitation_performance'); // الأداء
            $table->unsignedTinyInteger('sound'); // الصوت

            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('answers');
    }
};
