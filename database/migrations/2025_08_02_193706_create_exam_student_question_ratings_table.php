<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_student_question_ratings', function (
            Blueprint $table,
        ) {
            $table->uuid('id')->primary();
            $table
                ->foreignUuid('exam_student_question_id')
                ->constrained('exam_student_questions')
                ->cascadeOnDelete();
            $table->string('aspect');
            $table->unsignedInteger('mistakes')->default(0);
            $table->float('score')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_student_question_ratings');
    }
};
