<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('exam_student_question_ratings', function (
            Blueprint $table,
        ) {
            $table->float('weight')->nullable();
            $table->float('deduction_per_mistake')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('exam_student_question_ratings', function (
            Blueprint $table,
        ) {
            $table->dropColumn(['weight', 'deduction_per_mistake']);
        });
    }
};
