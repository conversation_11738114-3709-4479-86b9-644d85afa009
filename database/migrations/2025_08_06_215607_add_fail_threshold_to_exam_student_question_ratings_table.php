<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exam_student_question_ratings', function (
            Blueprint $table,
        ) {
            $table
                ->integer('fail_threshold')
                ->nullable()
                ->after('deduction_per_mistake');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exam_student_question_ratings', function (
            Blueprint $table,
        ) {
            $table->dropColumn('fail_threshold');
        });
    }
};
