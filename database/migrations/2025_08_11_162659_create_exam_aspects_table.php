<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_aspects', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table
                ->foreignUuid('profile_id')
                ->constrained('exam_setting_profiles')
                ->cascadeOnDelete();
            $table->string('key');
            $table->string('label');
            $table->decimal('weight', 6, 2); // e.g. 60.00
            $table->string('fail_aspect_key')->nullable();
            $table->unsignedInteger('sort')->default(0);
            $table->boolean('has_sub_aspects')->default(false);
            $table->timestamps();
            $table->unique(['profile_id', 'key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_aspects');
    }
};
