<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_sub_aspects', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table
                ->foreignUuid('aspect_id')
                ->constrained('exam_aspects')
                ->cascadeOnDelete();
            $table->string('key');
            $table->string('label');
            $table->decimal('deduction_per_mistake', 6, 2);
            $table->unsignedInteger('fail_threshold')->default(0);
            $table->unsignedInteger('sort')->default(0);
            $table->timestamps();
            $table->unique(['aspect_id', 'key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_sub_aspects');
    }
};
