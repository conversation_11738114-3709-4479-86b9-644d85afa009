<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exam_aspects', function (Blueprint $table) {
            $table->decimal('deduction_per_mistake', 8, 2)->nullable()->after('has_sub_aspects');
            $table->unsignedInteger('fail_threshold')->default(0)->after('deduction_per_mistake');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exam_aspects', function (Blueprint $table) {
            $table->dropColumn(['deduction_per_mistake', 'fail_threshold']);
        });
    }
};
