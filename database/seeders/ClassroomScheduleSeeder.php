<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Classroom;
use App\Models\ClassroomSchedule;
use Illuminate\Database\Seeder;

class ClassroomScheduleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing classrooms
        $classrooms = Classroom::all();

        if ($classrooms->isEmpty()) {
            $this->command->warn('No classrooms found. Please run TeacherSeeder and ClassroomSeeder first.');

            return;
        }

        // Clear existing schedules to avoid duplicates
        ClassroomSchedule::truncate();

        // Define weekdays (Sunday to Thursday for Middle Eastern academic week)
        $weekdays = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];

        // Define realistic time slots for Islamic education institution
        $timeSlots = [
            // Early morning sessions
            ['start' => '05:30', 'end' => '06:30'], // Fajr time
            ['start' => '06:00', 'end' => '07:00'], // Post-Fajr
            ['start' => '07:00', 'end' => '08:00'], // Early morning

            // Morning sessions
            ['start' => '08:00', 'end' => '09:30'], // Morning session 1
            ['start' => '09:00', 'end' => '10:30'], // Dhuha time
            ['start' => '09:30', 'end' => '11:00'], // Morning session 2
            ['start' => '10:00', 'end' => '11:30'], // Late morning
            ['start' => '10:30', 'end' => '12:00'], // Pre-Dhuhr

            // Afternoon sessions
            ['start' => '13:30', 'end' => '15:00'], // Post-Dhuhr
            ['start' => '14:00', 'end' => '15:30'], // Early afternoon
            ['start' => '15:00', 'end' => '16:30'], // Asr time
            ['start' => '15:30', 'end' => '17:00'], // Post-Asr
            ['start' => '16:00', 'end' => '17:30'], // Late afternoon

            // Evening sessions
            ['start' => '17:30', 'end' => '19:00'], // Pre-Maghrib
            ['start' => '19:30', 'end' => '21:00'], // Post-Maghrib
            ['start' => '20:00', 'end' => '21:30'], // Evening session
            ['start' => '21:00', 'end' => '22:00'], // Isha time
        ];

        $scheduleCount = 0;

        // Create schedules for each classroom
        foreach ($classrooms as $classroom) {
            // Determine schedule pattern based on classroom type
            $schedulesPerWeek = $this->getSchedulesPerWeek($classroom->name);

            // Generate schedules for this classroom
            for ($i = 0; $i < $schedulesPerWeek; $i++) {
                $weekday = $weekdays[array_rand($weekdays)];
                $timeSlot = $timeSlots[array_rand($timeSlots)];

                // Check if this time slot conflicts with existing schedule for this classroom
                $conflict = ClassroomSchedule::where('classroom_id', $classroom->id)
                    ->where('weekday', $weekday)
                    ->where(function ($query) use ($timeSlot) {
                        $query->whereBetween('start_time', [$timeSlot['start'], $timeSlot['end']])
                            ->orWhereBetween('end_time', [$timeSlot['start'], $timeSlot['end']])
                            ->orWhere(function ($q) use ($timeSlot) {
                                $q->where('start_time', '<=', $timeSlot['start'])
                                    ->where('end_time', '>=', $timeSlot['end']);
                            });
                    })
                    ->exists();

                // If no conflict, create the schedule
                if (! $conflict) {
                    ClassroomSchedule::create([
                        'classroom_id' => $classroom->id,
                        'weekday' => $weekday,
                        'start_time' => $timeSlot['start'],
                        'end_time' => $timeSlot['end'],
                        'is_active' => $this->getActiveStatus($classroom->name, $weekday),
                    ]);
                    $scheduleCount++;
                } else {
                    // Try a different time slot
                    $attempts = 0;
                    while ($conflict && $attempts < 10) {
                        $timeSlot = $timeSlots[array_rand($timeSlots)];
                        $conflict = ClassroomSchedule::where('classroom_id', $classroom->id)
                            ->where('weekday', $weekday)
                            ->where(function ($query) use ($timeSlot) {
                                $query->whereBetween('start_time', [$timeSlot['start'], $timeSlot['end']])
                                    ->orWhereBetween('end_time', [$timeSlot['start'], $timeSlot['end']])
                                    ->orWhere(function ($q) use ($timeSlot) {
                                        $q->where('start_time', '<=', $timeSlot['start'])
                                            ->where('end_time', '>=', $timeSlot['end']);
                                    });
                            })
                            ->exists();
                        $attempts++;
                    }

                    if (! $conflict) {
                        ClassroomSchedule::create([
                            'classroom_id' => $classroom->id,
                            'weekday' => $weekday,
                            'start_time' => $timeSlot['start'],
                            'end_time' => $timeSlot['end'],
                            'is_active' => $this->getActiveStatus($classroom->name, $weekday),
                        ]);
                        $scheduleCount++;
                    }
                }
            }
        }

        // Add some additional varied schedules to reach 50+ records
        $additionalSchedules = max(0, 50 - $scheduleCount);
        for ($i = 0; $i < $additionalSchedules; $i++) {
            $classroom = $classrooms->random();
            $weekday = $weekdays[array_rand($weekdays)];
            $timeSlot = $timeSlots[array_rand($timeSlots)];

            // Check for conflicts
            $conflict = ClassroomSchedule::where('classroom_id', $classroom->id)
                ->where('weekday', $weekday)
                ->where(function ($query) use ($timeSlot) {
                    $query->whereBetween('start_time', [$timeSlot['start'], $timeSlot['end']])
                        ->orWhereBetween('end_time', [$timeSlot['start'], $timeSlot['end']])
                        ->orWhere(function ($q) use ($timeSlot) {
                            $q->where('start_time', '<=', $timeSlot['start'])
                                ->where('end_time', '>=', $timeSlot['end']);
                        });
                })
                ->exists();

            if (! $conflict) {
                ClassroomSchedule::create([
                    'classroom_id' => $classroom->id,
                    'weekday' => $weekday,
                    'start_time' => $timeSlot['start'],
                    'end_time' => $timeSlot['end'],
                    'is_active' => rand(0, 10) > 1, // 90% active, 10% inactive
                ]);
                $scheduleCount++;
            }
        }

        $this->command->info("Created {$scheduleCount} classroom schedule records with realistic Islamic education timing.");
    }

    /**
     * Determine number of schedules per week based on classroom type
     */
    private function getSchedulesPerWeek(string $classroomName): int
    {
        // Prayer-time based classrooms typically have more frequent sessions
        if (str_contains($classroomName, 'الفجر') || str_contains($classroomName, 'المغرب')) {
            return rand(4, 5); // Daily or near-daily
        }

        if (str_contains($classroomName, 'الأطفال') || str_contains($classroomName, 'الشباب')) {
            return rand(3, 4); // 3-4 times per week
        }

        if (str_contains($classroomName, 'النساء') || str_contains($classroomName, 'كبار السن')) {
            return rand(2, 3); // 2-3 times per week
        }

        // Default for other classrooms
        return rand(3, 5);
    }

    /**
     * Determine active status based on classroom type and day
     */
    private function getActiveStatus(string $classroomName, string $weekday): bool
    {
        // Friday is typically less active in Islamic institutions
        if ($weekday === 'friday') {
            return rand(0, 10) > 7; // 30% chance of being active on Friday
        }

        // Special classrooms might have different patterns
        if (str_contains($classroomName, 'الفجر')) {
            return rand(0, 10) > 1; // 90% active for Fajr classes
        }

        if (str_contains($classroomName, 'الأطفال')) {
            return $weekday !== 'thursday' ? (rand(0, 10) > 2) : (rand(0, 10) > 5); // Less active on Thursday
        }

        // Default: 85% chance of being active
        return rand(0, 10) > 1.5;
    }
}
