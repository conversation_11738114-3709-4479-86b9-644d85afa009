<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Classroom;
use App\Models\Teacher;
use Illuminate\Database\Seeder;

class ClassroomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure we have teachers first
        $teachers = Teacher::all();

        if ($teachers->isEmpty()) {
            $this->command->warn('No teachers found. Please run TeacherSeeder first.');

            return;
        }

        // Create classrooms if they don't exist
        if (Classroom::count() === 0) {
            $classroomData = [
                ['name' => 'حلقة الفجر', 'description' => 'حلقة تحفيظ القرآن الكريم - فترة الفجر', 'capacity' => 15, 'room_number' => '101'],
                ['name' => 'حلقة الضحى', 'description' => 'حلقة تحفيظ القرآن الكريم - فترة الضحى', 'capacity' => 20, 'room_number' => '102'],
                ['name' => 'حلقة الظهر', 'description' => 'حلقة تحفيظ القرآن الكريم - فترة الظهر', 'capacity' => 18, 'room_number' => '103'],
                ['name' => 'حلقة العصر', 'description' => 'حلقة تحفيظ القرآن الكريم - فترة العصر', 'capacity' => 25, 'room_number' => '104'],
                ['name' => 'حلقة المغرب', 'description' => 'حلقة تحفيظ القرآن الكريم - فترة المغرب', 'capacity' => 22, 'room_number' => '105'],
                ['name' => 'حلقة العشاء', 'description' => 'حلقة تحفيظ القرآن الكريم - فترة العشاء', 'capacity' => 16, 'room_number' => '106'],
                ['name' => 'حلقة الأطفال الصغار', 'description' => 'حلقة مخصصة للأطفال الصغار', 'capacity' => 12, 'room_number' => '201'],
                ['name' => 'حلقة الشباب', 'description' => 'حلقة مخصصة للشباب', 'capacity' => 30, 'room_number' => '202'],
                ['name' => 'حلقة النساء', 'description' => 'حلقة مخصصة للنساء', 'capacity' => 20, 'room_number' => '203'],
                ['name' => 'حلقة كبار السن', 'description' => 'حلقة مخصصة لكبار السن', 'capacity' => 14, 'room_number' => '204'],
            ];

            foreach ($classroomData as $index => $data) {
                Classroom::create([
                    'name' => $data['name'],
                    'description' => $data['description'],
                    'teacher_id' => $teachers->get($index % $teachers->count())->id,
                    'capacity' => $data['capacity'],
                    'is_active' => true,
                    'room_number' => $data['room_number'],
                ]);
            }

            $this->command->info('Created 10 classrooms with realistic Islamic education data.');
        } else {
            $this->command->info('Classrooms already exist, skipping classroom creation.');
        }
    }
}
