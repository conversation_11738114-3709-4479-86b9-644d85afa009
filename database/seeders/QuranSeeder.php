<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Chapter;
use App\Models\Verse;
use Exception;
use Illuminate\Database\Seeder;
use Log;
use Str;

class QuranSeeder extends Seeder
{
    public function run(): void
    {
        $quranData = json_decode(
            file_get_contents(resource_path('json/QalounData_v2-1.json')),
            true
        );

        if (! $quranData) {
            throw new Exception('Failed to load QalounData_v2-1.json file');
        }

        $existingChapters = Chapter::pluck('id', 'number')->toArray();
        $chaptersToCreate = [];
        $processedChapters = [];

        foreach ($quranData as $record) {
            $chapterNumber = $record['sura_no'];

            if (isset($processedChapters[$chapterNumber]) ||
                isset($existingChapters[$chapterNumber])) {
                continue;
            }

            $chaptersToCreate[] = [
                'id' => (string) Str::uuid(),
                'number' => $chapterNumber,
                'name' => $record['sura_name_ar'],
                'name_en' => $record['sura_name_en'],
                'juz' => $record['jozz'],
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $processedChapters[$chapterNumber] = true;
        }

        if ($chaptersToCreate) {
            Chapter::insert($chaptersToCreate);
            $existingChapters = Chapter::pluck('id', 'number')->toArray(); // refresh
        }

        $versesToInsert = [];
        foreach ($quranData as $record) {
            $chapterNumber = $record['sura_no'];
            $chapterId = $existingChapters[$chapterNumber] ?? null;

            if (! $chapterId) {
                Log::warning("Chapter ID not found for chapter number {$chapterNumber}");

                continue;
            }

            // Does the verse text contain the Rub‑el‑Hizb symbol?
            $hasRubElHizb = Str::contains($record['aya_text'], '۞');

            $versesToInsert[] = [
                'id' => (string) Str::uuid(),
                'chapter_id' => $chapterId,
                'number' => $record['aya_no'],
                'text' => $record['aya_text'],
                'juz' => $record['jozz'],
                'page' => $record['page'],
                'line_start' => $record['line_start'],
                'line_end' => $record['line_end'],
                'eighth' => $hasRubElHizb ? 1 : 0,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        foreach (array_chunk($versesToInsert, 1000) as $chunk) {
            Verse::insert($chunk);
        }
    }
}
