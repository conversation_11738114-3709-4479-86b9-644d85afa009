<?php

declare(strict_types=1);

namespace Database\Seeders;

use <PERSON><PERSON><PERSON><PERSON><PERSON>h\FilamentShield\Support\Utils;
use Illuminate\Database\Seeder;
use Spatie\Permission\PermissionRegistrar;

class ShieldSeeder extends Seeder
{
    public static function makeDirectPermissions(string $directPermissions): void
    {
        if (! blank($permissions = json_decode($directPermissions, true))) {
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($permissions as $permission) {
                if ($permissionModel::whereName($permission)->doesntExist()) {
                    $permissionModel::create([
                        'name' => $permission['name'],
                        'guard_name' => $permission['guard_name'],
                    ]);
                }
            }
        }
    }

    public function run(): void
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        $rolesWithPermissions = '[{"name":"super_admin","guard_name":"web","permissions":["view_attendance::schedule","view_any_attendance::schedule","create_attendance::schedule","update_attendance::schedule","restore_attendance::schedule","restore_any_attendance::schedule","replicate_attendance::schedule","reorder_attendance::schedule","delete_attendance::schedule","delete_any_attendance::schedule","force_delete_attendance::schedule","force_delete_any_attendance::schedule","view_classroom","view_any_classroom","create_classroom","update_classroom","restore_classroom","restore_any_classroom","replicate_classroom","reorder_classroom","delete_classroom","delete_any_classroom","force_delete_classroom","force_delete_any_classroom","view_classroom::schedule","view_any_classroom::schedule","create_classroom::schedule","update_classroom::schedule","restore_classroom::schedule","restore_any_classroom::schedule","replicate_classroom::schedule","reorder_classroom::schedule","delete_classroom::schedule","delete_any_classroom::schedule","force_delete_classroom::schedule","force_delete_any_classroom::schedule","view_guardian","view_any_guardian","create_guardian","update_guardian","restore_guardian","restore_any_guardian","replicate_guardian","reorder_guardian","delete_guardian","delete_any_guardian","force_delete_guardian","force_delete_any_guardian","view_message::template","view_any_message::template","create_message::template","update_message::template","restore_message::template","restore_any_message::template","replicate_message::template","reorder_message::template","delete_message::template","delete_any_message::template","force_delete_message::template","force_delete_any_message::template","view_question","view_any_question","create_question","update_question","restore_question","restore_any_question","replicate_question","reorder_question","delete_question","delete_any_question","force_delete_question","force_delete_any_question","view_role","view_any_role","create_role","update_role","delete_role","delete_any_role","view_settings::setting","view_any_settings::setting","create_settings::setting","update_settings::setting","restore_settings::setting","restore_any_settings::setting","replicate_settings::setting","reorder_settings::setting","delete_settings::setting","delete_any_settings::setting","force_delete_settings::setting","force_delete_any_settings::setting","view_sms::message","view_any_sms::message","create_sms::message","update_sms::message","restore_sms::message","restore_any_sms::message","replicate_sms::message","reorder_sms::message","delete_sms::message","delete_any_sms::message","force_delete_sms::message","force_delete_any_sms::message","view_student","view_any_student","create_student","update_student","restore_student","restore_any_student","replicate_student","reorder_student","delete_student","delete_any_student","force_delete_student","force_delete_any_student","view_teacher","view_any_teacher","create_teacher","update_teacher","restore_teacher","restore_any_teacher","replicate_teacher","reorder_teacher","delete_teacher","delete_any_teacher","force_delete_teacher","force_delete_any_teacher","view_user","view_any_user","create_user","update_user","restore_user","restore_any_user","replicate_user","reorder_user","delete_user","delete_any_user","force_delete_user","force_delete_any_user","widget_ClassroomAttendanceReportStats","widget_ClassroomAttendanceStats"]}]';
        $directPermissions = '[]';

        static::makeRolesWithPermissions($rolesWithPermissions);
        static::makeDirectPermissions($directPermissions);

        $this->command->info('Shield Seeding Completed.');
    }

    protected static function makeRolesWithPermissions(string $rolesWithPermissions): void
    {
        if (! blank($rolePlusPermissions = json_decode($rolesWithPermissions, true))) {
            /** @var Model $roleModel */
            $roleModel = Utils::getRoleModel();
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($rolePlusPermissions as $rolePlusPermission) {
                $role = $roleModel::firstOrCreate([
                    'name' => $rolePlusPermission['name'],
                    'guard_name' => $rolePlusPermission['guard_name'],
                ]);

                if (! blank($rolePlusPermission['permissions'])) {
                    $permissionModels = collect($rolePlusPermission['permissions'])
                        ->map(fn ($permission) => $permissionModel::firstOrCreate([
                            'name' => $permission,
                            'guard_name' => $rolePlusPermission['guard_name'],
                        ]))
                        ->all();

                    $role->syncPermissions($permissionModels);
                }
            }
        }
    }
}
