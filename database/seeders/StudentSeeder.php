<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Classroom;
use App\Models\Guardian;
use App\Models\Student;
use Illuminate\Database\Seeder;

class StudentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $guardians = Guardian::all();
        $classrooms = Classroom::all();

        if ($guardians->isEmpty()) {
            $this->command->warn('No guardians found. Please run GuardianSeeder first.');

            return;
        }

        if ($classrooms->isEmpty()) {
            $this->command->warn('No classrooms found. Please run TeacherSeeder and ClassroomSeeder first.');

            return;
        }

        $registrationCounter = 1;

        foreach ($classrooms as $classroom) {
            for ($i = 0; $i < 10; $i++) {
                $guardian = $guardians->random();

                $student = [
                    'guardian_id' => $guardian->id,
                    'classroom_id' => $classroom->id,
                    'registration_number' => 'REG'.str_pad((string) $registrationCounter++, 3, '0', STR_PAD_LEFT),
                    'name' => fake()->name(),
                    'age' => rand(6, 14),
                    'date_of_birth' => now()->subYears(rand(6, 14))->subMonths(rand(0, 11))->subDays(rand(0, 30))->toDateString(),
                    'grade_level' => 'Grade '.rand(1, 8),
                    'gender' => fake()->randomElement(['male', 'female']),
                    'medical_conditions' => fake()->boolean(20) ? fake()->sentence(3) : null,
                    'notes' => fake()->boolean(50) ? fake()->sentence(6) : null,
                    'is_active' => true,
                ];

                Student::create($student);
            }
        }

        $this->command->info('10 students created for each classroom.');
    }
}
