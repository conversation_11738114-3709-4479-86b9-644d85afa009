<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Teacher;
use Illuminate\Database\Seeder;

class TeacherSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create teachers if they don't exist
        if (Teacher::count() === 0) {
            Teacher::factory()->count(10)->create();
            $this->command->info('Created 10 teachers for classroom assignments.');
        } else {
            $this->command->info('Teachers already exist, skipping teacher creation.');
        }
    }
}
