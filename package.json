{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "prettier:format": "prettier --write ."}, "devDependencies": {"@prettier/plugin-php": "^0.22.4", "@shufo/prettier-plugin-blade": "^1.15.3", "@tailwindcss/vite": "^4.1.11", "@types/react-dom": "^19.1.6", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.4", "vite": "^6.2.4", "vite-plugin-svgr": "^4.3.0"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@inertiajs/inertia": "^0.11.1", "@inertiajs/react": "^2.0.12", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@solar-icons/react": "^1.0.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-react": "^4.5.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.23.6", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.74"}}