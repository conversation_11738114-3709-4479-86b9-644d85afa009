/* Arabic Fonts for Quran Text */
@font-face {
    font-family: 'UthmanicQaloun';
    src: url('../fonts/UthmanicQaloun V21.ttf') format('truetype'),
         url('../fonts/uthmanic_qaloun_v21.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'qaloon-v8';
    src: url('../fonts/uthmanic_hafs_v22.ttf') format('truetype'),
         url('../fonts/qaloon-v8-full-org.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Base Arabic text styling */
.arabic-verse-text {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    font-size: 18px !important;
    line-height: 1.8 !important;
    text-align: right !important;
    direction: rtl !important;
    color: #1e1e1e;
    font-weight: normal;
}

/* Table column specific styling */
.fi-ta-text .arabic-verse-text {
    font-size: 16px !important;
    line-height: 1.6 !important;
    padding: 4px 8px;
}

/* Select dropdown options styling */
.fi-select-option.arabic-option {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    text-align: right !important;
    direction: rtl !important;
    padding: 8px 12px !important;
}

/* Select input field styling */
.fi-select-input.arabic-select {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    font-size: 16px !important;
    text-align: right !important;
    direction: rtl !important;
}

/* Choices.js dropdown styling for Arabic */
.choices__list--dropdown .choices__item.arabic-choice {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    text-align: right !important;
    direction: rtl !important;
    padding: 8px 12px !important;
}

.choices__input.arabic-input {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    text-align: right !important;
    direction: rtl !important;
}

/* Dark mode support */
:root.dark .arabic-verse-text {
    color: #e5e5e5;
}

/* Responsive font sizes */
@media (max-width: 768px) {
    .arabic-verse-text {
        font-size: 16px !important;
        line-height: 1.7 !important;
    }
    
    .fi-ta-text .arabic-verse-text {
        font-size: 14px !important;
        line-height: 1.5 !important;
    }
}

/* Ensure proper rendering on different browsers */
.arabic-verse-text {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Fix for Filament table cell alignment */
.fi-ta-cell .arabic-verse-text {
    width: 100%;
    display: block;
}

/* Select component wrapper styling */
.arabic-select-wrapper .fi-select-input {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    text-align: right !important;
    direction: rtl !important;
}

/* Ensure dropdown options inherit Arabic styling */
.arabic-select-wrapper .choices__list--dropdown .choices__item {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    text-align: right !important;
    direction: rtl !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
}
