(()=>{var f=({canTrackDeselectedRecords:h,currentSelectionLivewireProperty:c,maxSelectableRecords:s,$wire:l})=>({checkboxClickController:null,collapsedGroups:[],isLoading:!1,selectedRecords:new Set,deselectedRecords:new Set,isTrackingDeselectedRecords:!1,shouldCheckUniqueSelection:!0,lastCheckedRecord:null,livewireId:null,entangledSelectedRecords:c?l.$entangle(c):null,init(){this.livewireId=this.$root.closest("[wire\\:id]").attributes["wire:id"].value,l.$on("deselectAllTableRecords",()=>this.deselectAllRecords()),c&&(s!==1?this.selectedRecords=new Set(this.entangledSelectedRecords):this.selectedRecords=new Set(this.entangledSelectedRecords?[this.entangledSelectedRecords]:[])),this.$nextTick(()=>this.watchForCheckboxClicks()),Livewire.hook("element.init",({component:e})=>{e.id===this.livewireId&&this.watchForCheckboxClicks()})},mountAction(...e){l.set("isTrackingDeselectedTableRecords",this.isTrackingDeselectedRecords,!1),l.set("selectedTableRecords",[...this.selectedRecords],!1),l.set("deselectedTableRecords",[...this.deselectedRecords],!1),l.mountAction(...e)},toggleSelectRecordsOnPage(){let e=this.getRecordsOnPage();if(this.areRecordsSelected(e)){this.deselectRecords(e);return}this.selectRecords(e)},toggleSelectRecords(e){this.areRecordsSelected(e)?this.deselectRecords(e):this.selectRecords(e)},getSelectedRecordsCount(){return this.isTrackingDeselectedRecords?(this.$refs.allSelectableRecordsCount?.value??this.deselectedRecords.size)-this.deselectedRecords.size:this.selectedRecords.size},getRecordsOnPage(){let e=[];for(let t of this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[])e.push(t.value);return e},selectRecords(e){s===1&&(this.deselectAllRecords(),e=e.slice(0,1));for(let t of e)if(!this.isRecordSelected(t)){if(this.isTrackingDeselectedRecords){this.deselectedRecords.delete(t);continue}this.selectedRecords.add(t)}this.updatedSelectedRecords()},deselectRecords(e){for(let t of e){if(this.isTrackingDeselectedRecords){this.deselectedRecords.add(t);continue}this.selectedRecords.delete(t)}this.updatedSelectedRecords()},updatedSelectedRecords(){if(s!==1){this.entangledSelectedRecords=[...this.selectedRecords];return}this.entangledSelectedRecords=[...this.selectedRecords][0]??null},toggleSelectedRecord(e){if(this.isRecordSelected(e)){this.deselectRecords([e]);return}this.selectRecords([e])},async selectAllRecords(){if(!h){this.isLoading=!0,this.selectedRecords=new Set(await l.getAllSelectableTableRecordKeys()),this.updatedSelectedRecords(),this.isLoading=!1;return}this.isTrackingDeselectedRecords=!0,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},canSelectAllRecords(e){if(e){let i=this.getRecordsOnPage();return!this.areRecordsSelected(i)&&this.areRecordsToggleable(i)}let t=parseInt(this.$refs.allSelectableRecordsCount?.value);if(!t)return!1;let d=this.getSelectedRecordsCount();return t===d?!1:s===null||t<=s},deselectAllRecords(){this.isTrackingDeselectedRecords=!1,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},isRecordSelected(e){return this.isTrackingDeselectedRecords?!this.deselectedRecords.has(e):this.selectedRecords.has(e)},areRecordsSelected(e){return e.every(t=>this.isRecordSelected(t))},areRecordsToggleable(e){if(s===null||s===1)return!0;let t=e.filter(d=>this.isRecordSelected(d));return t.length===e.length?!0:this.getSelectedRecordsCount()+(e.length-t.length)<=s},toggleCollapseGroup(e){if(this.isGroupCollapsed(e)){this.collapsedGroups.splice(this.collapsedGroups.indexOf(e),1);return}this.collapsedGroups.push(e)},isGroupCollapsed(e){return this.collapsedGroups.includes(e)},resetCollapsedGroups(){this.collapsedGroups=[]},watchForCheckboxClicks(){this.checkboxClickController&&this.checkboxClickController.abort(),this.checkboxClickController=new AbortController;let{signal:e}=this.checkboxClickController;this.$root?.addEventListener("click",t=>t.target?.matches(".fi-ta-record-checkbox")&&this.handleCheckboxClick(t,t.target),{signal:e})},handleCheckboxClick(e,t){if(!this.lastChecked){this.lastChecked=t;return}if(e.shiftKey){let d=Array.from(this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[]);if(!d.includes(this.lastChecked)){this.lastChecked=t;return}let i=d.indexOf(this.lastChecked),r=d.indexOf(t),n=[i,r].sort((a,g)=>a-g),o=[];for(let a=n[0];a<=n[1];a++)o.push(d[a].value);if(t.checked){if(!this.areRecordsToggleable(o)){t.checked=!1,this.deselectRecords([t.value]);return}this.selectRecords(o)}else this.deselectRecords(o)}this.lastChecked=t}});function u({columns:h,isLive:c}){return{error:void 0,isLoading:!1,columns:h,isLive:c,init(){if(!this.columns||this.columns.length===0){this.columns=[];return}},get groupedColumns(){let s={};return this.columns.filter(l=>l.type==="group").forEach(l=>{s[l.name]=this.calculateGroupedColumns(l)}),s},calculateGroupedColumns(s){if((s?.columns?.filter(i=>!i.isHidden)??[]).length===0)return{hidden:!0,checked:!1,disabled:!1,indeterminate:!1};let e=s.columns.filter(i=>!i.isHidden&&i.isToggleable!==!1);if(e.length===0)return{checked:!0,disabled:!0,indeterminate:!1};let t=e.filter(i=>i.isToggled).length,d=s.columns.filter(i=>!i.isHidden&&i.isToggleable===!1);return t===0&&d.length>0?{checked:!0,disabled:!1,indeterminate:!0}:t===0?{checked:!1,disabled:!1,indeterminate:!1}:t===e.length?{checked:!0,disabled:!1,indeterminate:!1}:{checked:!0,disabled:!1,indeterminate:!0}},getColumn(s,l=null){return l?this.columns.find(t=>t.type==="group"&&t.name===l)?.columns?.find(t=>t.name===s):this.columns.find(e=>e.name===s)},toggleGroup(s){let l=this.columns.find(r=>r.type==="group"&&r.name===s);if(!l?.columns)return;let e=this.calculateGroupedColumns(l);if(e.disabled)return;let d=l.columns.filter(r=>r.isToggleable!==!1).some(r=>r.isToggled),i=e.indeterminate?!0:!d;l.columns.filter(r=>r.isToggleable!==!1).forEach(r=>{r.isToggled=i}),this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager()},toggleColumn(s,l=null){let e=this.getColumn(s,l);!e||e.isToggleable===!1||(e.isToggled=!e.isToggled,this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager())},reorderColumns(s){let l=s.map(e=>e.split("::"));this.reorderTopLevel(l),this.isLive&&this.applyTableColumnManager()},reorderGroupColumns(s,l){let e=this.columns.find(i=>i.type==="group"&&i.name===l);if(!e)return;let t=s.map(i=>i.split("::")),d=[];t.forEach(([i,r])=>{let n=e.columns.find(o=>o.name===r);n&&d.push(n)}),e.columns=d,this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager()},reorderTopLevel(s){let l=this.columns,e=[];s.forEach(([t,d])=>{let i=l.find(r=>t==="group"?r.type==="group"&&r.name===d:t==="column"?r.type!=="group"&&r.name===d:!1);i&&e.push(i)}),this.columns=e},async applyTableColumnManager(){this.isLoading=!0;try{await this.$wire.call("applyTableColumnManager",this.columns),this.error=void 0}catch(s){this.error="Failed to update column visibility",console.error("Table toggle columns error:",s)}finally{this.isLoading=!1}}}}document.addEventListener("alpine:init",()=>{window.Alpine.data("filamentTable",f),window.Alpine.data("filamentTableColumnManager",u)});})();
