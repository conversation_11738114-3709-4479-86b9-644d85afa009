import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import ChapterName from './ChapterName';
import Verse from './Verse';
import PageNumber from './PageNumber';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

interface Chapter {
    id: string;
    name: string;
}

interface Verse {
    id: string;
    text: string;
    juz: number;
    page: number;
    number: number;
    chapter_id: number;
    chapter: Chapter;
}

interface GroupedVerses {
    [key: string]: Verse[];
}

interface QuranViewerProps {}

const QuranViewer: React.FC<QuranViewerProps> = () => {
    const [grouped, setGrouped] = useState<GroupedVerses>({});
    const [selectedChapter, setSelectedChapter] = useState<string>('');
    const [search, setSearch] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [chapters, setChapters] = useState<Chapter[]>([]);
    const [verses, setVerses] = useState<Verse[]>([]);
    const [page, setPage] = useState(1);
    let lastChapterName = '';

    useEffect(() => {
        const fetchInitialData = async () => {
            const [chaptersResponse, versesResponse] = await Promise.all([
                axios.get('/api/chapters'),
                axios.get('/verses', { params: { page } }),
            ]);
            setChapters(chaptersResponse.data);
            setVerses(versesResponse.data);
        };
        fetchInitialData();
    }, []);

    useEffect(() => {
        const newGrouped = verses.reduce((acc: GroupedVerses, verse: Verse) => {
            const key = verse.page;
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(verse);
            return acc;
        }, {});
        setGrouped(newGrouped);
    }, [verses]);

    const sentinelRef = useInfiniteScroll({
        hasMore,
        loading,
        onLoadMore: () => setPage((prev) => prev + 1),
    });

    useEffect(() => {
        const fetchVerses = async () => {
            setLoading(true);
            try {
                const response = await axios.get('/verses', {
                    params: {
                        page,
                        selectedChapter,
                        search,
                    },
                });
                const newVerses = response.data;
                if (newVerses.length === 0) {
                    setHasMore(false);
                }

                if (page === 1) {
                    setVerses(newVerses);
                } else {
                    setVerses((prev) => [...prev, ...newVerses]);
                }
            } catch (error) {
                console.error('Error fetching verses:', error);
            }
            setLoading(false);
        };
        fetchVerses();
    }, [page, selectedChapter, search]);

    useEffect(() => {
        setPage(1);
        setVerses([]);
        setHasMore(true);
    }, [selectedChapter, search]);

    return (
        <div className="absolute bottom-0 left-0 right-0 flex flex-col max-w-3xl px-4 pt-8 pb-10 mx-auto top-1">
            <div className="flex-shrink-0 z-10 flex justify-between bg-[#F3E9D9]">
                <Select
                    onValueChange={(value) => setSelectedChapter(value)}
                    value={selectedChapter}
                    dir="rtl"
                >
                    <SelectTrigger className="w-full bg-secondary-500">
                        <SelectValue placeholder="اختر السورة" />
                    </SelectTrigger>
                    <SelectContent>
                        {chapters.map((chapter) => (
                            <SelectItem key={chapter.id} value={chapter.id}>
                                {chapter.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            <div className="flex-1 pt-8 overflow-y-auto scrollbar-hide">
                {Object.entries(grouped).map(([pageNumber, verseGroup]) => {
                    const currentChapterName =
                        verseGroup[0]?.chapter.name ?? 'غير معروف';
                    const showChapterName =
                        currentChapterName !== lastChapterName;
                    lastChapterName = currentChapterName;
                    return (
                        <VerseGroup
                            key={pageNumber}
                            verses={verseGroup}
                            pageNumber={parseInt(pageNumber)}
                            showChapterName={showChapterName}
                        />
                    );
                })}
                <div ref={sentinelRef} />
                {loading && (
                    <div className="p-4 text-center text-gray-600">
                        تحميل المزيد من الآيات...
                    </div>
                )}
            </div>
        </div>
    );
};

export default QuranViewer;

interface VerseGroupProps {
    verses: Verse[];
    pageNumber: number;
    showChapterName: boolean;
}

const VerseGroup: React.FC<VerseGroupProps> = ({
    verses,
    pageNumber,
    showChapterName,
}) => {
    const chapterName = verses[0]?.chapter.name ?? 'غير معروف';
    const juz = verses[0]?.juz;

    return (
        <div className="mb-8">
            {showChapterName && (
                <div className="flex justify-center pb-3">
                    <ChapterName name={chapterName} />
                </div>
            )}
            <div
                className="text-right text-[22px] text-gray-800 leading-relaxed pb-2"
                style={{ textAlign: 'justify' }}
            >
                <p className="leading-[48px]">
                    {verses.map((verse) => (
                        <Verse key={verse.id} verse={verse} />
                    ))}
                </p>
            </div>
            <div className="flex justify-end pt-4 border-t-1 border-secondary-500">
                {juz && <PageNumber page={pageNumber} juz={juz} />}
            </div>
        </div>
    );
};
