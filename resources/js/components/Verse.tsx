import React, { useRef } from 'react';
import {
    useQuestionCreation,
    Verse as VerseType,
} from '@/contexts/question-creation-context';

interface VerseProps {
    verse: VerseType;
}

const Verse: React.FC<VerseProps> = ({ verse }) => {
    const { handleVerseClick } = useQuestionCreation();
    const words = verse.text.split(' ');
    const lastWord = words.pop() || '';
    const text = words.join(' ');
    const match = lastWord.match(/^(.*)\s(.+)$/);

    const spanRef = useRef<HTMLSpanElement>(null);

    const handleClick = () => {
        spanRef.current?.focus();

        if (handleVerseClick) handleVerseClick(verse);
    };

    return (
        <span
            ref={spanRef}
            tabIndex={0}
            onClick={handleClick}
            className="p-2 rounded-md verse-text font-qaloon hover:bg-secondary-500 focus:bg-secondary-500"
        >
            {text} {match ? match[1] : ''}
            <span className="verse-number tarqim">{match ? match[2] : ''}</span>
        </span>
    );
};

export default Verse;
