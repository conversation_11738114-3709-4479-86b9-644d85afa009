'use client';

import * as React from 'react';
import { format, parse } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Calendar as CalendarIcon } from '@solar-icons/react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';

import { usePage, router } from '@inertiajs/react';

interface DatePickerProps {
    initialDate: Date;
}

export default function DatePicker({ initialDate }: DatePickerProps) {
    const [date, setDate] = React.useState<Date>(initialDate);
    const dayName = format(date, 'EEEE', { locale: ar });

    const handleSelect = (selected: Date | undefined) => {
        if (!selected) return;

        setDate(selected); // Update local UI immediately

        const params = new URLSearchParams(window.location.search);
        params.set('date', format(selected, 'yyyy-MM-dd'));

        router.visit(`${window.location.pathname}?${params.toString()}`, {
            preserveScroll: true,
            preserveState: true,
        });
    };

    return (
        <div className="flex justify-between bg-secondary-200 rounded-md p-3">
            <div>
                <h3 className="text-[15px] pb-2">التاريخ</h3>
                <h1 className="text-secondary-900 text-[23px] font-bold">
                    {format(date, 'yyyy/MM/dd')}
                </h1>
            </div>

            <div>
                <h3 className="text-[15px] font-bold pb-2">{dayName}</h3>
                <Popover>
                    <PopoverTrigger asChild>
                        <Button className="shadow-none">
                            <CalendarIcon weight="Linear" size={18} />
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                        <Calendar
                            required
                            mode="single"
                            selected={date}
                            onSelect={handleSelect}
                        />
                    </PopoverContent>
                </Popover>
            </div>
        </div>
    );
}
