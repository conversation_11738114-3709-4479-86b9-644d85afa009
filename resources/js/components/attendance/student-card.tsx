import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import BottomSheet from '@/components/ui/bottom-sheet';

import { cx } from 'class-variance-authority';
import { CheckCircle, CloseCircle } from '@solar-icons/react';

import { Student } from '@/types/attendance';
import { router } from '@inertiajs/react';
import { format } from 'date-fns';

interface StudentCardProps {
    student: Student;
    attendanceDate: Date;
}

export default function StudentCard({
    student,
    attendanceDate,
}: StudentCardProps) {
    const { attendance, name } = student;
    const hasAttendance = attendance && attendance.length > 0;

    const [sheetOpen, setSheetOpen] = useState(false);
    const [attendanceStatus, setAttendanceStatus] = useState<string>(
        hasAttendance ? attendance[0].type : 'Absent',
    );
    const [notes, setNotes] = useState<string>('');

    const handleTakeAttendance = (attendanceType: 'Present' | 'Absent') => {
        console.log('here');
        router.post(
            route('attendance.store', route().params.classroom),
            {
                student_id: student.id,
                type: attendanceType,
                notes: notes,
                date: format(attendanceDate, 'yyyy-MM-dd'),
            },
            {
                onSuccess: () => {
                    setSheetOpen(false);
                    // Optionally, you can refresh the page or update the state to reflect the new attendance
                },
                onError: (error) => {
                    console.error('Failed to take attendance:', error);
                },
            },
        );
    };

    const handleUpdateAttendance = () => {
        router.put(
            route('attendance.update', route().params.classroom),
            {
                attendance_id: attendance[0].id,
                student_id: student.id,
                type: attendanceStatus,
                notes: notes,
                date: format(attendanceDate, 'yyyy-MM-dd'),
            },
            {
                onSuccess: () => {
                    setSheetOpen(false);
                    // Optionally, you can refresh the page or update the state to reflect the updated attendance
                },
                onError: (error) => {
                    console.error('Failed to update attendance:', error);
                },
            },
        );
    };

    return (
        <div
            className="flex items-center overflow-hidden justify-between h-[58px] ps-2.5 bg-secondary-100 border border-secondary-300 rounded-md"
            onClick={() => {
                if (hasAttendance) {
                    setSheetOpen(true);
                }
            }}
        >
            <h3 className="text-[15px] font-bold flex-1">{name}</h3>

            {hasAttendance ? (
                <h3
                    className={cx(
                        'text-[15px] font-bold flex-1 text-end h-full flex items-center justify-end pe-2.5',
                        attendance[0].type === 'Present'
                            ? 'text-primary-700'
                            : 'text-[#A60D10]',
                    )}
                    style={{
                        background:
                            'linear-gradient(270deg, rgba(198, 153, 76, 0.00) 0%, rgba(198, 153, 76, 0.71) 100%)',
                        backdropFilter: 'blur(12px)',
                    }}
                >
                    {attendance[0].type === 'Present' ? 'حضور' : 'غياب'}
                </h3>
            ) : (
                <div className="flex gap-2 pe-2.5">
                    <Button
                        className="bg-primary-600 rounded-md w-[38px] h-[38px] hover:bg-primary-700"
                        onClick={() => handleTakeAttendance('Present')}
                    >
                        <CheckCircle
                            weight="Linear"
                            height={100}
                            width={100}
                            className="text-white"
                        />
                    </Button>

                    <Button
                        className="bg-secondary-600 rounded-md w-[38px] h-[38px]"
                        onClick={() => setSheetOpen(true)}
                    >
                        <CloseCircle weight="Linear" />
                    </Button>
                </div>
            )}

            <BottomSheet open={sheetOpen} setOpen={setSheetOpen}>
                <div className="flex flex-col gap-8 px-8">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-[21px] font-bold">{name}</h1>
                            <p className="text-lg text-primary-500 font-bold">
                                {student.registration_number}
                            </p>
                        </div>

                        <div className="flex flex-col items-end">
                            {hasAttendance ? (
                                <Select
                                    value={attendanceStatus}
                                    onValueChange={setAttendanceStatus}
                                >
                                    <SelectTrigger className="bg-secondary-600 rounded-full w-[150px] text-[#11181c] border-none">
                                        <SelectValue
                                            placeholder="اختر الحالة"
                                            className="placeholder-[#11181c] text-[#11181c] text-[16px] font-bold"
                                        />
                                    </SelectTrigger>
                                    <SelectContent className="bg-secondary-500 rounded-xl">
                                        <SelectItem value="Present">
                                            حاضر
                                        </SelectItem>
                                        <SelectItem value="Absent">
                                            غياب
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            ) : (
                                <h1 className="text-left text-[21px] font-bold text-[#A60D10]">
                                    غياب
                                </h1>
                            )}
                            <p className="text-lg text-primary-500">
                                2025-08-21
                            </p>
                        </div>
                    </div>

                    <textarea
                        placeholder="ملاحظات"
                        className="bg-secondary-200 p-4 border border-secondary-500 rounded-[12px] h-[150px] focus-visible:outline-none"
                        onChange={(e) => setNotes(e.target.value)}
                    />

                    <Button
                        className="p-3 h-12 bg-primary-500 text-white text-[16px] rounded-xl hover:bg-primary-600"
                        onClick={
                            hasAttendance
                                ? () => handleUpdateAttendance()
                                : () => handleTakeAttendance('Absent')
                        }
                    >
                        {hasAttendance ? 'تعديل السجل' : 'تسجيل الغياب'}
                    </Button>
                </div>
            </BottomSheet>
        </div>
    );
}
