import { Classrooms, Schedule } from '@/types/classroom';
import dayjs from 'dayjs';
import 'dayjs/locale/ar';

interface ClassRoomCardProps {
    classroom: Classrooms;
    schedule?: Schedule;
}

const statusClasses = {
    upcoming:
        'bg-background-100 border border-secondary-600/50 text-primary-500',
    current: 'bg-secondary-100 border border-primary-500/50 text-primary-700',
    past: 'bg-muted text-muted-foreground border border-border',
};

const statusLabels = {
    upcoming: 'لم تبدأ بعد',
    current: 'الحلقة جارية الآن',
    past: 'انتهت الحلقة',
    inactive: 'تم إغلاق الحلقة',
};

export default function ClassroomCard({
    classroom,
    schedule,
}: ClassRoomCardProps) {
    const status = schedule ? getScheduleStatus(schedule) : 'inactive';
    const statusClass = statusClasses[status === 'inactive' ? 'past' : status]; // fallback style
    const label = statusLabels[status];
    const dayName = dayjs().locale('ar').format('dddd');

    return (
        <div
            className={`rounded-xl p-4 shadow-sm transition-transform hover:scale-105 ${statusClass}`}
        >
            <div className="flex justify-between items-center">
                <p className="text-lg font-bold text-secondary-700">
                    {classroom.name}
                </p>
                <span className="text-sm text-secondary-800">
                    القاعة {classroom.room_number}
                </span>
            </div>

            <div className="mt-2 text-sm">
                {schedule
                    ? schedule.is_active
                        ? 'متوفرة'
                        : 'غير متوفرة'
                    : `لا يوجد جدول لهذا اليوم (${dayName})`}
            </div>

            {schedule && (
                <>
                    <div className="mt-1 text-sm">
                        من {schedule.start_time} إلى {schedule.end_time}
                    </div>
                </>
            )}
            <div className="mt-1 text-xs font-semibold">{label}</div>
        </div>
    );
}

function getScheduleStatus(
    schedule: Schedule,
): 'upcoming' | 'current' | 'past' {
    const now = dayjs();
    const today = dayjs().format('YYYY-MM-DD');
    const start = dayjs(`${today}T${schedule.start_time}`);
    const end = dayjs(`${today}T${schedule.end_time}`);

    if (now.isBefore(start)) return 'upcoming';
    if (now.isAfter(end)) return 'past';
    return 'current';
}
