import Logo from '@/assets/Logo.svg';
import { User } from '@solar-icons/react';
import { usePage } from '@inertiajs/react';
import { SharedData } from '@/types';

export default function MobileHeader() {
    const { auth } = usePage<SharedData>().props;

    return (
        <header className="relative flex bg-secondary-100 p-[30px] pb-[18.62px] items-center justify-between">
            {/* <div className="absolute bottom-0 left-0 w-full h-8 bg-white [clip-path:ellipse(60%_100%_at_50%_100%)]"></div> */}

            <div className="flex items-center gap-2">
                <div className="flex justify-center items-center bg-white rounded-full border-[3px] border-gray-200">
                    <img
                        src={`https://ui-avatars.com/api/?name=${auth.user.name}&color=00190e&background=345d4ac2`}
                        alt="Profile"
                        className="object-fill w-11 h-11 rounded-full"
                    />
                </div>
                <div>
                    <h1 className="text-[15px] font-bold text-primary-500">
                        مساكم الله بالخير
                    </h1>
                    <p className="text-[17px] font-bold text-secondary-500">
                        {auth.user.name}
                    </p>
                </div>
            </div>
            <Logo className="h-16 w-16" />
        </header>
    );
}
