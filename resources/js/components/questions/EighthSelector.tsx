import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { useQuestionCreation } from '@/contexts/question-creation-context';
import { Verse } from '@/types/questions';

interface EighthSelectorProps {
    label: string;
    eighths: Verse[];
}

function EighthSelector({ label, eighths }: EighthSelectorProps) {
    const { verseId, setVerseId } = useQuestionCreation();

    const groupedEighths = eighths.reduce(
        (acc, eighth) => {
            const chapterName = eighth.chapter.name;
            if (!acc[chapterName]) {
                acc[chapterName] = [];
            }
            acc[chapterName].push(eighth);
            return acc;
        },
        {} as Record<string, Verse[]>,
    );

    return (
        <div className="flex flex-col gap-1.5 bg-secondary-100 border border-secondary-300 p-2 rounded-md">
            <h3 className="font-bold font-bona-reqular text-primary-500">
                {label}
            </h3>
            <Select onValueChange={(value) => setVerseId(value)} dir="rtl">
                <SelectTrigger className="bg-secondary-600 rounded-full text-[#11181c] border-none">
                    <SelectValue placeholder="اختر الثمن" />
                </SelectTrigger>
                <SelectContent>
                    {Object.entries(groupedEighths).map(
                        ([chapterName, verses]) => (
                            <SelectGroup key={chapterName}>
                                <SelectLabel>{chapterName}</SelectLabel>
                                {verses.map((eighth) => (
                                    <SelectItem
                                        key={eighth.id}
                                        value={eighth.id.toString()}
                                    >
                                        {eighth.text.slice(0, 50)}...
                                    </SelectItem>
                                ))}
                            </SelectGroup>
                        ),
                    )}
                </SelectContent>
            </Select>
        </div>
    );
}

export default EighthSelector;
