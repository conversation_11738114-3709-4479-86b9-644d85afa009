import { Button } from '@/components/ui/button';
import { useQuestionCreation } from '@/contexts/question-creation-context';
import QuestionSection from './question-section';

type VerseType = 'start' | 'end';

interface VerseSelectorProps {
    label: string;
    type: VerseType;
}

function VerseSelector({ label, type }: VerseSelectorProps) {
    const { openBottomSheet, startVerse, endVerse } = useQuestionCreation();

    const selectedVerse = type === 'start' ? startVerse : endVerse;

    if (selectedVerse) {
        return (
            <div
                className="cursor-pointer"
                onClick={() => openBottomSheet(type)}
            >
                <QuestionSection title={label} aya={selectedVerse.text} />
            </div>
        );
    }

    return (
        <>
            <div className="flex flex-col gap-1.5 bg-secondary-100 border border-secondary-300 p-2 rounded-md">
                <h3 className="font-bold font-bona-reqular text-primary-500">
                    {label}
                </h3>

                <Button
                    variant="default"
                    className="font-bona-reqular bg-primary-500 text-white rounded-xl self-center w-[250px]"
                    onClick={() => openBottomSheet(type)}
                >
                    عرض المصحف
                </Button>
            </div>
        </>
    );
}

export default VerseSelector;
