import { useQuestionCreation } from '@/contexts/question-creation-context';
import BottomSheet from '@/components/ui/bottom-sheet';
import QuranViewer from '@/components/QuranViewer';

export default function ViewerBottomsheet() {
    const { isBottomSheetOpen, closeBottomSheet } = useQuestionCreation();

    return (
        <BottomSheet open={isBottomSheetOpen} setOpen={closeBottomSheet}>
            <QuranViewer />
        </BottomSheet>
    );
}
