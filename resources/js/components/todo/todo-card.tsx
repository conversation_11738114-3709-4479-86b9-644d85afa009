import React, { useRef, useState } from 'react';
import { motion, useMotionValue, useTransform } from 'framer-motion';
import { TrashBinTrash, CheckSquare } from '@solar-icons/react';
import Snackbar from '@/components/ui/snack-bar';
import { Todo } from '@/hooks/useTodos';
import { useTodoActions } from '@/hooks/useTodoActions';

interface TodoCardProps {
    todo: Todo;
    onTodoUpdated?: (todo: Todo) => void;
    onTodoDeleted?: (id: string) => void;
}

export function TodoCard({
    todo,
    onTodoUpdated,
    onTodoDeleted,
}: TodoCardProps) {
    const { toggleTodoDone, deleteTodo } = useTodoActions();
    const [showSnackbar, setShowSnackbar] = useState(false);

    const x = useMotionValue(0);
    const handleDelete = async () => {
        const success = await deleteTodo(todo.id);
        if (success && onTodoDeleted) {
            onTodoDeleted(todo.id);
        }
    };

    const handleToggleDone = async () => {
        const updatedTodo = await toggleTodoDone(todo.id, todo.is_done);
        if (updatedTodo && onTodoUpdated) {
            onTodoUpdated(updatedTodo);
            if (!todo.is_done) {
                setShowSnackbar(true);
            }
        }
    };

    const handleUndone = async () => {
        const updatedTodo = await toggleTodoDone(todo.id, true);
        if (updatedTodo && onTodoUpdated) {
            onTodoUpdated(updatedTodo);
        }
        setShowSnackbar(false);
    };

    return (
        <>
            <div className="relative select-none">
                <div className="absolute inset-0 flex rounded-2xl overflow-hidden pointer-events-none">
                    <div className="flex-1 flex items-center bg-secondary-500 ps-6">
                        <CheckSquare
                            type="Bold"
                            size={24}
                            className="text-secondary-900"
                        />
                    </div>
                    <div className="flex-1 flex items-center bg-secondary-500 justify-end pe-6">
                        <TrashBinTrash
                            type="Bold"
                            size={24}
                            className="text-secondary-900"
                        />
                    </div>
                </div>
                <motion.div
                    drag="x"
                    dragConstraints={{ left: 0, right: 0 }}
                    style={{ x }}
                    onDragEnd={(event, info) => {
                        if (info.offset.x > 100) {
                            handleDelete();
                        } else if (info.offset.x < -100) {
                            handleToggleDone();
                        }
                    }}
                    className={`px-4 py-3 mb-3 rounded-xl text-right shadow-sm will-change-transform ${
                        todo.is_done
                            ? 'line-through bg-secondary-300'
                            : 'bg-[#F0E8DB]'
                    }`}
                >
                    <div className="flex flex-col justify-between items-start mb-1">
                        <span className="text-xs text-gray-500">
                            {todo.created_at}
                        </span>
                        <h3 className="font-semibold text-secondary-900 text-sm">
                            {todo.title}
                        </h3>
                    </div>
                    <p className="text-sm text-secondary-800 leading-relaxed">
                        {todo.body}
                    </p>
                </motion.div>
            </div>

            <Snackbar
                show={showSnackbar}
                message="تم تحديد المهمة كمكتملة"
                actionLabel="تراجع"
                onAction={handleUndone}
                onClose={() => setShowSnackbar(false)}
            />
        </>
    );
}
