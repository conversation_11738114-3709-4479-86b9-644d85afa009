import dayjs from 'dayjs';
import { motion } from 'framer-motion';
import { TodoCard } from '@/components/todo/todo-card';
import { Todo, GroupedTodos } from '@/hooks/useTodos';

interface TodoCardsProps {
    groupedTodos: GroupedTodos;
    onTodoUpdated?: (todo: Todo) => void;
    onTodoDeleted?: (id: string) => void;
}

export default function TodoCards({
    groupedTodos,
    onTodoUpdated,
    onTodoDeleted,
}: TodoCardsProps) {
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: { y: 0, opacity: 1 },
    };

    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
        >
            {Object.keys(groupedTodos).length ? (
                Object.entries(groupedTodos).map(([date, todos]) => (
                    <motion.div
                        key={date}
                        variants={itemVariants}
                        className="space-y-2"
                    >
                        <h3 className="px-4 py-2 font-semibold text-secondary-800">
                            {dayjs(date).format('YYYY/MM/DD')}
                        </h3>
                        {todos.map((todo) => (
                            <TodoCard
                                key={todo.id}
                                todo={todo}
                                onTodoUpdated={onTodoUpdated}
                                onTodoDeleted={onTodoDeleted}
                            />
                        ))}
                    </motion.div>
                ))
            ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                    لا توجد مهام حالياً 🎉
                </p>
            )}
        </motion.div>
    );
}
