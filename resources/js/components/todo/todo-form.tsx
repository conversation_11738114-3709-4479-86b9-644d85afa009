import React, { useState, FormEvent } from 'react';
import axios from 'axios';
import { z } from 'zod';
import { Todo } from '@/hooks/useTodos';

const formSchema = z.object({
    title: z.string().min(1, { message: 'العنوان مطلوب' }),
    body: z.string().min(1, { message: 'الوصف مطلوب' }),
    classroom_id: z.string().min(1, { message: 'معرف الفصل مطلوب' }),
});

type FormInput = z.infer<typeof formSchema>;
type FormErrors = Record<keyof FormInput, string | undefined>;

interface TodoFormProps {
    classroomId: string;
    onTodoCreated?: (todo: Todo) => void;
    onClose?: () => void;
}

export default function TodoForm({
    classroomId,
    onTodoCreated,
    onClose,
}: TodoFormProps) {
    const [processing, setProcessing] = useState(false);
    const [data, setData] = useState<FormInput>({
        title: '',
        body: '',
        classroom_id: classroomId,
    });
    const [errors, setErrors] = useState<FormErrors>({
        title: undefined,
        body: undefined,
        classroom_id: undefined,
    });

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        setErrors({ title: undefined, body: undefined, classroom_id: undefined });

        const result = formSchema.safeParse(data);
        if (!result.success) {
            const fieldErrors: Partial<FormErrors> = {};
            result.error.issues.forEach((issue) => {
                const key = issue.path[0] as keyof FormInput;
                if (!fieldErrors[key]) {
                    fieldErrors[key] = issue.message;
                }
            });
            setErrors((prev) => ({ ...prev, ...fieldErrors }));
            return;
        }

        setProcessing(true);
        try {
            const response = await axios.post(route('todo.store'), data, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });
            if (onTodoCreated && response.data.todo) {
                onTodoCreated(response.data.todo);
            }
            setData({ title: '', body: '', classroom_id: classroomId });
            if (onClose) {
                onClose();
            }
        } catch (error: any) {
            if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }
        } finally {
            setProcessing(false);
        }
    };

    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const { name, value } = e.target;
        setData((prev) => ({ ...prev, [name]: value }));
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-5 text-right">
            <div>
                <label className="block mb-1 text-sm font-medium text-secondary-900">
                    العنوان
                </label>
                <input
                    type="text"
                    name="title"
                    value={data.title}
                    onChange={handleChange}
                    className="w-full rounded-lg border border-secondary-300 bg-secondary-50 px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring focus:ring-secondary-500/30"
                    required
                />
                {errors.title && (
                    <p className="text-sm text-red-600 mt-1">{errors.title}</p>
                )}
            </div>

            <div>
                <label className="block mb-1 text-sm font-medium text-secondary-900">
                    الوصف
                </label>
                <textarea
                    name="body"
                    value={data.body}
                    onChange={handleChange}
                    className="w-full rounded-lg border border-secondary-300 bg-secondary-50 px-3 py-2 text-sm shadow-sm resize-none focus:outline-none focus:ring focus:ring-secondary-500/30"
                    rows={4}
                    required
                />
                {errors.body && (
                    <p className="text-sm text-red-600 mt-1">{errors.body}</p>
                )}
            </div>

            <button
                type="submit"
                disabled={processing}
                className="w-full bg-primary-500 font-baloo-regular text-white py-2.5 rounded-lg transition hover:bg-primary-600 disabled:opacity-50"
            >
                {processing ? 'جار الإضافة...' : 'إضافة المهمة'}
            </button>
        </form>
    );
}
