import { useState } from 'react';
import { EyeClosed, Eye } from '@solar-icons/react';
import { Input } from '@/components/ui/input';
import InputError from '@/components/input-error';

interface PasswordInputProps {
    data: any;
    setData: any;
    errors: any;
}

function PasswordInput({ data, setData, errors }: PasswordInputProps) {
    const [showPassword, setShowPassword] = useState(false);
    const IconSize = 30;

    return (
        <div className="grid gap-2">
            <div className="relative flex max-w-[380px] h-[60px] mb-4 items-center rounded-md border border-[#E4D6BD] bg-background-300 text-right px-4">
                <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    tabIndex={2}
                    autoComplete="current-password"
                    value={data.password}
                    onChange={(e) => setData('password', e.target.value)}
                    placeholder="كلمة المرور"
                    className="flex-1 font-bold text-[26px] text-secondary-900 text-right border-none shadow-none bg-transparent outline-none focus-visible:ring-0 focus-visible:border-transparent focus:ring-0 focus:border-transparent"
                />

                <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute p-2 left-4 text-secondary-500 hover:text-secondary-700 focus:outline-none bg-background-300"
                    tabIndex={-1}
                >
                    {showPassword ? (
                        <Eye size={IconSize} weight="Linear" />
                    ) : (
                        <EyeClosed size={IconSize} weight="Linear" />
                    )}
                </button>
            </div>

            <InputError message={errors.password} />
        </div>
    );
}

export default PasswordInput;
