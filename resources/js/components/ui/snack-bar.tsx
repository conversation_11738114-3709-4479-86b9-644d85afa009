import { useEffect } from 'react';
import { CloseCircle, Reply } from '@solar-icons/react';

interface SnackbarProps {
    show: boolean;
    message: string;
    actionLabel?: string;
    onAction?: () => void;
    onClose: () => void;
    duration?: number;
}

export default function Snackbar({
    show,
    message,
    actionLabel = 'تراجع',
    onAction,
    onClose,
    duration = 5000,
}: SnackbarProps) {
    useEffect(() => {
        if (!show) return;

        const t = setTimeout(onClose, duration);

        return () => clearTimeout(t);
    }, [show, duration, onClose]);

    if (!show) return null;

    return (
        <div className="fixed bottom-4 right-0 left-0 z-50 px-4 w-full flex justify-center">
            <div
                dir="rtl"
                className="w-full max-w-md rounded-xl px-4 py-3
                bg-secondary-200 text-secondary-800 shadow-md border border-secondary-300
                flex justify-between items-center gap-4 transition-all scale-100 opacity-100 pointer-events-auto"
            >
                <button onClick={onClose} className="shrink-0">
                    <CloseCircle
                        type="Bold"
                        size={20}
                        className="text-secondary-900"
                    />
                </button>
                <span className="flex-1 text-sm break-words">{message}</span>

                {onAction && (
                    <button
                        onClick={onAction}
                        className="text-sm font-medium underline underline-offset-2 shrink-0 flex items-center gap-2"
                    >
                        {actionLabel}
                        <Reply
                            type="Bold"
                            size={20}
                            className="text-secondary-900"
                        />
                    </button>
                )}
            </div>
        </div>
    );
}
