import {
    createContext,
    useContext,
    useState,
    ReactNode,
    Dispatch,
    SetStateAction,
} from 'react';
import { Verse } from '@/types/questions';

type VerseType = 'start' | 'end';

interface QuestionCreationContextType {
    verseId: string | null;
    setVerseId: Dispatch<SetStateAction<string | null>>;
    startVerse: Verse | null;
    endVerse: Verse | null;
    activeVerseType: VerseType | null;
    handleVerseClick: (verse: Verse) => void;
    isBottomSheetOpen: boolean;
    openBottomSheet: (type: VerseType) => void;
    closeBottomSheet: () => void;
}

const QuestionCreationContext = createContext<
    QuestionCreationContextType | undefined
>(undefined);

export const useQuestionCreation = () => {
    const context = useContext(QuestionCreationContext);
    if (!context) {
        throw new Error(
            'useQuestionCreation must be used within a QuestionCreationProvider',
        );
    }
    return context;
};

interface QuestionCreationProviderProps {
    children: ReactNode;
    initialVerse?: string | null;
    initialStartVerse?: Verse | null;
    initialEndVerse?: Verse | null;
}

export const QuestionCreationProvider = ({
    children,
    initialVerse = null,
    initialStartVerse = null,
    initialEndVerse = null,
}: QuestionCreationProviderProps) => {
    const [verseId, setVerseId] = useState<string | null>(initialVerse);
    const [startVerse, setStartVerse] = useState<Verse | null>(
        initialStartVerse,
    );
    const [endVerse, setEndVerse] = useState<Verse | null>(initialEndVerse);
    const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
    const [activeVerseType, setActiveVerseType] = useState<VerseType | null>(
        null,
    );

    const handleVerseClick = (verse: Verse) => {
        const isVerseBefore = (verseA: Verse, verseB: Verse) => {
            if (verseA.chapter_id < verseB.chapter_id) {
                return true;
            }
            if (
                verseA.chapter_id === verseB.chapter_id &&
                verseA.number < verseB.number
            ) {
                return true;
            }
            return false;
        };

        if (activeVerseType === 'start') {
            if (endVerse && isVerseBefore(endVerse, verse)) {
                setStartVerse(endVerse);
                setEndVerse(verse);
            } else {
                setStartVerse(verse);
            }
            if (endVerse && verse.id === endVerse.id) {
                setEndVerse(null);
            }
        } else if (activeVerseType === 'end') {
            if (startVerse && isVerseBefore(verse, startVerse)) {
                setEndVerse(startVerse);
                setStartVerse(verse);
            } else {
                setEndVerse(verse);
            }
            if (startVerse && verse.id === startVerse.id) {
                setStartVerse(null);
            }
        }
        setIsBottomSheetOpen(false);
    };

    const openBottomSheet = (type: VerseType) => {
        setActiveVerseType(type);
        setIsBottomSheetOpen(true);
    };

    const closeBottomSheet = () => {
        setIsBottomSheetOpen(false);
    };

    return (
        <QuestionCreationContext.Provider
            value={{
                verseId: verseId,
                setVerseId: setVerseId,
                startVerse,
                endVerse,
                activeVerseType,
                handleVerseClick,
                isBottomSheetOpen,
                openBottomSheet,
                closeBottomSheet,
            }}
        >
            {children}
        </QuestionCreationContext.Provider>
    );
};
