import { useEffect, useRef } from 'react';

interface UseInfiniteScrollProps {
    hasMore: boolean;
    loading: boolean;
    onLoadMore: () => void;
}

export function useInfiniteScroll({ hasMore, loading, onLoadMore }: UseInfiniteScrollProps) {
    const sentinelRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        const target = sentinelRef.current;
        if (!target) return;

        const observer = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting && hasMore && !loading) {
                onLoadMore();
            }
        });

        observer.observe(target);
        return () => observer.disconnect();
    }, [hasMore, loading, onLoadMore]);

    return sentinelRef;
}
