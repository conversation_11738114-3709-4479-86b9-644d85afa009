import { useState, useCallback } from 'react';
import axios from 'axios';
import { Todo } from './useTodos';

export function useTodoActions() {
    const [loading, setLoading] = useState(false);

    const deleteTodo = useCallback(async (id: string): Promise<boolean> => {
        if (!confirm('هل أنت متأكد من حذف هذه المهمة؟')) {
            return false;
        }

        setLoading(true);
        try {
            await axios.delete(route('todo.destroy', id), {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });
            return true;
        } catch (error) {
            console.error('Error deleting todo:', error);
            return false;
        } finally {
            setLoading(false);
        }
    }, []);

    const restoreTodo = useCallback(async (id: string): Promise<Todo | null> => {
        setLoading(true);
        try {
            const response = await axios.post(
                route('todo.restore', id),
                {},
                {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                }
            );
            return response.data.todo || null;
        } catch (error) {
            console.error('Error restoring todo:', error);
            return null;
        } finally {
            setLoading(false);
        }
    }, []);

    const toggleTodoDone = useCallback(async (id: string, isDone: boolean): Promise<Todo | null> => {
        setLoading(true);
        const endpoint = isDone
            ? route('todo.undone', id)
            : route('todo.done', id);
        try {
            const response = await axios.post(
                endpoint,
                {},
                {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                }
            );
            return response.data.todo || null;
        } catch (error) {
            console.error('Error toggling todo:', error);
            return null;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        loading,
        deleteTodo,
        restoreTodo,
        toggleTodoDone,
    };
}
