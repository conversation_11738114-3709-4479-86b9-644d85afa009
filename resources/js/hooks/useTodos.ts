import { useState, useCallback } from 'react';
import axios from 'axios';
import dayjs from 'dayjs';

export interface Todo {
    id: string;
    title: string;
    body: string;
    created_at: string;
    created_date: string;
    is_done: boolean;
}

export interface GroupedTodos {
    [date: string]: Todo[];
}

export function useTodos(classroomId: string) {
    const [groupedTodos, setGroupedTodos] = useState<GroupedTodos>({});
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);

    const fetchTodos = useCallback(
        async (pageNum: number, filter: 'all' | 'today' = 'all') => {
            setLoading(true);
            try {
                const url = route('todos', {
                    classroom: classroomId,
                    page: pageNum,
                    filter,
                });
                const { data } = await axios.get(url);
                setHasMore(Boolean(data.meta.next_page_url));
                setGroupedTodos((prev) => {
                    const groups = pageNum === 1 ? {} : { ...prev };
                    data.todos.forEach((todo: Todo) => {
                        const key = dayjs(todo.created_date).format(
                            'YYYY-MM-DD',
                        );
                        if (!groups[key]) groups[key] = [];
                        groups[key].push(todo);
                    });
                    return groups;
                });
                setPage(pageNum);
            } catch (e) {
                console.error(e);
            } finally {
                setLoading(false);
            }
        },
        [classroomId],
    );

    const addTodo = useCallback((todo: Todo) => {
        setGroupedTodos((prev) => {
            const key = dayjs(todo.created_date).format('YYYY-MM-DD');
            const newGroups = { ...prev };
            const arr = newGroups[key] ?? [];
            newGroups[key] = [todo, ...arr];

            const sortedKeys = Object.keys(newGroups).sort((a, b) =>
                dayjs(b).diff(dayjs(a)),
            );

            const sortedGroups: GroupedTodos = {};
            for (const sortedKey of sortedKeys) {
                sortedGroups[sortedKey] = newGroups[sortedKey];
            }

            return sortedGroups;
        });
    }, []);

    const updateTodo = useCallback((updatedTodo: Todo) => {
        setGroupedTodos((prev) => {
            const groups = { ...prev };
            for (const [date, list] of Object.entries(groups)) {
                groups[date] = list.map((todo) =>
                    todo.id === updatedTodo.id ? updatedTodo : todo,
                );
            }
            return groups;
        });
    }, []);

    const removeTodo = useCallback((todoId: string) => {
        setGroupedTodos((prev) => {
            const updated = { ...prev };
            for (const [date, list] of Object.entries(updated)) {
                updated[date] = list.filter((todo) => todo.id !== todoId);
                if (updated[date].length === 0) delete updated[date];
            }
            return updated;
        });
    }, []);

    const reset = useCallback(() => {
        setGroupedTodos({});
        setPage(1);
        setHasMore(true);
    }, []);

    return {
        groupedTodos,
        page,
        hasMore,
        loading,
        fetchTodos,
        addTodo,
        updateTodo,
        removeTodo,
        reset,
    };
}
