import { AppContent } from '@/components/app-content';
import { AppShell } from '@/components/app-shell';
import { AppSidebar } from '@/components/app-sidebar';
import { AppSidebarHeader } from '@/components/app-sidebar-header';
import MobileHeader from '@/components/mobile-header';
import NavigationHeader from '@/components/navigation-header';
import { type BreadcrumbItem } from '@/types';
import { type PropsWithChildren } from 'react';

export default function AppSidebarLayout({
    children,
    title,
    backLink,
}: PropsWithChildren<{
    title?: string;
    backLink?: string;
}>) {
    return (
        <AppShell>
            <AppContent>
                <MobileHeader />
                {backLink && title && (
                    <NavigationHeader title={title} backLink={backLink} />
                )}
                {/* <AppSidebarHeader breadcrumbs={breadcrumbs} /> */}
                {children}
            </AppContent>
        </AppShell>
    );
}
