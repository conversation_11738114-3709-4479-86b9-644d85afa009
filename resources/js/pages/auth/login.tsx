import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import AuthLayout from '@/layouts/auth-layout';
import PasswordInput from '@/components/ui/password-input';

import { z } from 'zod';
interface LoginProps {
    status?: string;
}

const formSchema = z.object({
    phone: z
        .string()
        .min(9, { message: 'Phone is required' })
        .max(9, { message: 'Phone must be 9 digits' }),

    password: z.string().min(1, { message: 'Password is required' }),

    remember: z.boolean().optional(),
});
type FormInput = z.infer<typeof formSchema>;

export default function TeacherLogin({ status }: LoginProps) {
    const {
        data,
        setData,
        transform,
        post,
        processing,
        errors,
        setError,
        reset,
    } = useForm<FormInput>({
        phone: '',
        password: '',
        remember: false,
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        const result = formSchema.safeParse(data);
        if (!result.success) {
            const fieldErrors: Record<string, string> = {};
            result.error.issues.forEach((issue) => {
                const key = issue.path[0] as keyof FormInput;
                if (!fieldErrors[key]) fieldErrors[key] = issue.message;
            });
            setError(fieldErrors);
            return;
        }
        const originalPhone = data.phone;
        const formattedPhone = originalPhone.startsWith('00218')
            ? originalPhone
            : `00218${originalPhone}`;
        // Temporarily update data in useForm
        transform((data) => ({ ...data, phone: formattedPhone }));
        post(route('login'), {
            onFinish: () => {
                reset('password');
            },
            onError: (errors) => {
                console.log(errors);
            },
        });
    };

    return (
        <AuthLayout title="مرحبا بك معنا في" description="منصة المعلم">
            <Head title="Teacher Login" />

            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-[5px]">
                    <div className="grid gap-2">
                        <div
                            dir="ltr"
                            className="flex h-[60px] mb-4 items-center justify-between rounded-md border border-[#E4D6BD] bg-background-300 text-right px-4"
                        >
                            <span className="text-primary-500 font-bold text-[26px] pe-4 border-secondary-500 border-r-2">
                                +218
                            </span>
                            <Input
                                id="phone"
                                type="phone"
                                required
                                autoFocus
                                tabIndex={1}
                                autoComplete="phone"
                                value={data.phone}
                                onChange={(e) =>
                                    setData('phone', e.target.value)
                                }
                                placeholder="91 123 4567"
                                className="flex-1 font-bold text-[26px] text-secondary-900 text-right border-none shadow-none bg-transparent outline-none focus-visible:ring-0 focus-visible:border-transparent focus:ring-0 focus:border-transparent"
                            />
                        </div>
                        <InputError message={errors.phone} />
                    </div>

                    <PasswordInput
                        data={data}
                        setData={setData}
                        errors={errors}
                    />

                    <div className="flex items-center justify-between gap-4">
                        <span className="text-secondary-900 font-bold text-[18px]">
                            تذكّرني في المرة المقبلة
                        </span>
                        <label className="relative inline-flex items-center cursor-pointer">
                            <Input
                                id="remember"
                                type="checkbox"
                                className="sr-only peer"
                                checked={data.remember}
                                onChange={() =>
                                    setData('remember', !data.remember)
                                }
                            />
                            <div className="w-12 h-6 bg-gray-300 peer-checked:bg-primary-500 rounded-full transition-colors peer-checked:after:-translate-x-6 after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
                        </label>
                    </div>

                    <Button
                        type="submit"
                        className="w-full mt-4 text-[20px] h-[48px] bg-primary-500 hover:bg-primary-700 text-white"
                        tabIndex={4}
                        disabled={processing}
                    >
                        {processing && (
                            <LoaderCircle className="w-4 h-4 animate-spin" />
                        )}
                        تسجيل الدخول
                    </Button>
                </div>
            </form>

            {status && (
                <div className="mb-4 text-sm font-medium text-center text-green-600">
                    {status}
                </div>
            )}
        </AuthLayout>
    );
}
