import DatePicker from '@/components/attendance/date-picker';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import { Classroom } from '@/types/attendance';
import StudentCard from '@/components/attendance/student-card';
import { parse } from 'date-fns';

interface AttendancePageProps {
    classroom: Classroom;
    backlink: string;
}

export default function Attendance({
    classroom,
    backlink,
}: AttendancePageProps) {
    const { url } = usePage();
        const searchParams = new URLSearchParams(url.split('?')[1] || '');
        const queryDate = searchParams.get('date');
    
        const date = queryDate
            ? parse(queryDate, 'yyyy-MM-dd', new Date())
            : new Date();
    return (
        <AppLayout title="الحضور والانصراف" backLink={backlink}>
            <Head title="Attendance" />
            <div className="container mx-auto p-8">
                <DatePicker initialDate={date} />

                <div className="flex flex-col gap-5 pt-5">
                    {classroom.students.map((student) => (
                        <StudentCard key={student.id} student={student} attendanceDate={date} />
                    ))}
                </div>
            </div>
        </AppLayout>
    );
}
