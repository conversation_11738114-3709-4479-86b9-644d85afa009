import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import TodoList from '@/components/todo/todo-list';
import { Schedule } from '@/types/classroom';

interface Classroom {
    id: string;
    name: string;
    description: string;
    capacity: number;
    is_active: boolean;
    room_number: number;
    schedules: Schedule[];
}

interface Props {
    classroom: {
        data: Classroom;
    };
}

export default function ClassRoom({ classroom }: Props) {
    // create a link list
    const links = [
        {
            title: 'الحضور',
            href: route('attendance', {
                classroom: classroom.data.id,
            }),
        },
        {
            title: 'الإختبارات',
            href: route('classroom', {
                classroom: classroom.data.id,
            }),
        },
        {
            title: 'التقارير',
            href: route('classroom', {
                classroom: classroom.data.id,
            }),
        },
        {
            title: 'الإعدادات',
            href: route('classroom', {
                classroom: classroom.data.id,
            }),
        },
    ];

    return (
        <AppLayout>
            <Head title={classroom.data.name} />
            <div className="p-4 space-y-6">
                {/* Navigation Buttons */}
                <div className="grid grid-cols-2 gap-4">
                    {links.map((link) => (
                        <Link
                            key={link.title}
                            href={link.href}
                            prefetch
                            className="flex items-center justify-center rounded-lg bg-secondary-100 p-4 text-sm font-medium text-secondary-700 hover:bg-secondary-200"
                        >
                            {link.title}
                        </Link>
                    ))}
                </div>
                <TodoList classroom_id={classroom.data.id} />
            </div>
        </AppLayout>
    );
}
