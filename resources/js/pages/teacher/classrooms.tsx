import { Head, Link, usePage } from '@inertiajs/react';
import Logo from '@/assets/Logo.svg';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { SharedData } from '@/types';
import { Classrooms } from '@/types/classroom';
import ClassroomCard from '@/components/classroom-card';

dayjs.extend(isBetween);

interface ClassRoomProps {
    classrooms: Classrooms[];
    dayName: string;
    now: string;
}

export default function ClassRoom({ classrooms }: ClassRoomProps) {
    const { auth } = usePage<SharedData>().props;
    const classroomCards = classrooms.map((classroom) => {
        return (
            <Link
                href={route('classroom', {
                    classroom: classroom.id,
                })}
                prefetch
                key={classroom.id}
                className="space-y-2"
            >
                <ClassroomCard
                    key={`${classroom.id}`}
                    classroom={classroom}
                    schedule={
                        classroom.schedules?.length > 0
                            ? classroom.schedules[0]
                            : undefined
                    }
                />
            </Link>
        );
    });

    return (
        <div className="min-h-screen max-w-screen-lg m-auto bg-background text-right px-6 py-4 flex flex-col items-center">
            <Head title="Classroom" />

            {/* Header */}
            <div className="w-full flex justify-between items-center mb-10">
                <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white shadow-md">
                    <img
                        src={`https://ui-avatars.com/api/?name=${auth.user.name}&color=00190e&background=345d4ac2`}
                        alt={auth.user.name}
                        className="object-cover w-full h-full"
                    />
                </div>
                <div>
                    <Logo className="h-16 w-16" />
                </div>
            </div>

            {/* Title */}
            <div className="flex flex-col items-center mt-10">
                <h2 className="text-[32px] font-bold text-secondary-600">
                    مركز القلم لتحفيظ القرآن
                </h2>
                <h3 className="text-[32px] font-bold text-primary-500 mb-10">
                    الحلقات التعليمية
                </h3>
            </div>

            {/* Classrooms */}
            <div className="w-full max-w-md flex flex-col space-y-4 mt-auto mb-10">
                {classroomCards}
            </div>
        </div>
    );
}
