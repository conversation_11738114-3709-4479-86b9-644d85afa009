import { useEffect, useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import axios from 'axios';

import AppLayout from '@/layouts/app-layout';
import VerseSelector from '@/components/questions/verse-selector';
import ViewerBottomsheet from '@/components/questions/viewer-bottomsheet';
import EighthSelector from '@/components/questions/EighthSelector';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';

import {
    QuestionCreationProvider,
    useQuestionCreation,
} from '@/contexts/question-creation-context';

import { Verse } from '@/types/questions';

interface CreateQuestionPageProps {
    backlink: string;
    classroom: { id: string };
}

function CreateQuestionPage({ backlink, classroom }: CreateQuestionPageProps) {
    const { verseId, startVerse, endVerse } = useQuestionCreation();
    const [questionType, setQuestionType] = useState<'by_aya' | 'by_eighth'>(
        'by_aya',
    );
    const [eighths, setEighths] = useState<Verse[]>([]);
    const { data, setData, post, processing, errors } = useForm({
        verse_id: '',
        start_verse_id: '',
        end_verse_id: '',
    });

    useEffect(() => {
        axios.get('/api/eighths').then((response) => {
            setEighths(response.data);
        });
    }, []);

    useEffect(() => {
        if (questionType === 'by_eighth') {
            setData({
                verse_id: verseId || '',
                start_verse_id: '',
                end_verse_id: '',
            });
        } else {
            setData({
                verse_id: '',
                start_verse_id: startVerse?.id || '',
                end_verse_id: endVerse?.id || '',
            });
        }
    }, [verseId, startVerse, endVerse, questionType]);

    function submit(e: React.FormEvent) {
        e.preventDefault();
        post(route('questions.store', { classroom: classroom.id }));
    }

    return (
        <AppLayout title="انشاء سؤال جديد" backLink={backlink}>
            <Head title="Add Question" />
            <form onSubmit={submit} className="flex flex-col flex-1 gap-8 p-8">
                <div className="w-full bg-secondary-500 flex rounded-lg p-1 relative">
                    <button
                        type="button"
                        onClick={() => setQuestionType('by_aya')}
                        className="px-4 flex-1 py-2 text-sm font-medium rounded-md text-secondary-700 z-10"
                    >
                        بالآية
                    </button>
                    <button
                        type="button"
                        onClick={() => setQuestionType('by_eighth')}
                        className="px-4 py-2 flex-1 text-sm font-medium rounded-md text-secondary-700 z-10"
                    >
                        بالثمن
                    </button>
                    {questionType === 'by_aya' && (
                        <motion.div
                            layoutId="active-tab"
                            className="absolute inset-0 bg-secondary-300 rounded-md shadow"
                            style={{
                                width: '50%',
                                right: '0%',
                            }}
                        />
                    )}
                    {questionType === 'by_eighth' && (
                        <motion.div
                            layoutId="active-tab"
                            className="absolute inset-0 bg-secondary-300 rounded-md shadow"
                            style={{
                                width: '50%',
                                right: '50%',
                            }}
                        />
                    )}
                </div>
                {questionType === 'by_aya' ? (
                    <>
                        <div className="flex flex-col gap-2.5 bg-secondary-200 border-secondary-600 p-2.5 rounded-md">
                            <VerseSelector label="من الآية" type="start" />
                        </div>
                        <div className="flex flex-col gap-2.5 bg-secondary-200 border-secondary-600 p-2.5 rounded-md">
                            <VerseSelector label="إلى الآية" type="end" />
                        </div>
                    </>
                ) : (
                    <div className="flex flex-col gap-2.5 bg-secondary-200 border-secondary-600 p-2.5 rounded-md">
                        <EighthSelector label="اختر الثمن" eighths={eighths} />
                    </div>
                )}
                <Button
                    type="submit"
                    className="h-12 mt-auto text-white bg-primary-500 rounded-xl"
                    disabled={processing}
                >
                    إنشاء السؤال
                </Button>
            </form>

            <ViewerBottomsheet />
        </AppLayout>
    );
}

function CreateQuestionPageWrapper(props: CreateQuestionPageProps) {
    return (
        <QuestionCreationProvider>
            <CreateQuestionPage {...props} />
        </QuestionCreationProvider>
    );
}

export default CreateQuestionPageWrapper;
