import { Head, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import VerseSelector from '@/components/questions/verse-selector';
import ViewerBottomsheet from '@/components/questions/viewer-bottomsheet';
import { Button } from '@/components/ui/button';
import {
    QuestionCreationProvider,
    useQuestionCreation,
} from '@/contexts/question-creation-context';
import { Input } from '@/components/ui/input';
import { useEffect } from 'react';
import { Question } from '@/types/questions';

interface EditQuestionPageProps {
    backlink: string;
    classroom: { id: string };
    question: Question;
}

function EditQuestionPage({
    backlink,
    classroom,
    question,
}: EditQuestionPageProps) {
    const { startVerse, endVerse } = useQuestionCreation();
    const { data, setData, put, processing, errors } = useForm({
        start_verse_id: question.start_verse.id,
        end_verse_id: question.end_verse.id,
    });

    useEffect(() => {
        setData({
            ...data,
            start_verse_id: startVerse?.id || data.start_verse_id,
            end_verse_id: endVerse?.id || data.end_verse_id,
        });
    }, [startVerse, endVerse]);

    function submit(e: React.FormEvent) {
        e.preventDefault();
        put(
            route('questions.update', {
                classroom: classroom.id,
                question: question.id,
            }),
        );
    }

    return (
        <AppLayout title="تعديل السؤال" backLink={backlink}>
            <Head title="Edit Question" />
            <form onSubmit={submit} className="flex flex-col flex-1 gap-8 p-8">
                <div className="flex flex-col gap-2.5 bg-secondary-200 border-secondary-600 p-2.5 rounded-md">
                    <VerseSelector label="من الآية" type="start" />
                </div>
                <div className="flex flex-col gap-2.5 bg-secondary-200 border-secondary-600 p-2.5 rounded-md">
                    <VerseSelector label="إلى الآية" type="end" />
                </div>
                <Button
                    type="submit"
                    className="h-12 mt-auto text-white bg-primary-500 rounded-xl"
                    disabled={processing}
                >
                    تعديل السؤال
                </Button>
            </form>

            <ViewerBottomsheet />
        </AppLayout>
    );
}

function EditQuestionPageWrapper(props: EditQuestionPageProps) {
    return (
        <QuestionCreationProvider
            initialStartVerse={props.question.start_verse}
            initialEndVerse={props.question.end_verse}
        >
            <EditQuestionPage {...props} />
        </QuestionCreationProvider>
    );
}

export default EditQuestionPageWrapper;
