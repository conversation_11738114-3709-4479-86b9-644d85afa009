import { useEffect, useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import axios from 'axios';

import QuestionSection from '@/components/questions/question-section';
import AppLayout from '@/layouts/app-layout';
import { AddCircle, TrashBinTrash, FileText } from '@solar-icons/react';
import { Button } from '@/components/ui/button';
import { Classroom, Question, Verse } from '@/types/questions';
import { ConfirmationModal } from '@/components/ui/confirmation-modal';

interface QuestionsPageProps {
    backlink: string;
    classroom: Classroom;
    questions: Question[];
}

function QuestionsPage({ backlink, classroom, questions }: QuestionsPageProps) {
    const { delete: deleteQuestion } = useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(
        null,
    );
    const [eighths, setEighths] = useState<Verse[]>([]);
    const [processedQuestions, setProcessedQuestions] = useState<Question[]>(
        [],
    );

    useEffect(() => {
        axios.get('/api/eighths').then((response) => {
            setEighths(response.data);
        });
    }, []);

    useEffect(() => {
        if (questions.length > 0 && eighths.length > 0) {
            const newQuestions = questions.map((question) => {
                if (question.verse) {
                    const startVerse = eighths.find(
                        (eighth) => eighth.id === question.verse.id,
                    );
                    if (startVerse) {
                        const nextEighth = eighths.find(
                            (eighth) => eighth.id > startVerse.id,
                        );
                        if (nextEighth) {
                            return {
                                ...question,
                                start_verse: startVerse,
                                end_verse: nextEighth,
                            };
                        }
                    }
                }
                return question;
            });
            console.log(newQuestions);
            setProcessedQuestions(newQuestions);
        } else {
            setProcessedQuestions(questions);
        }
    }, [questions, eighths]);

    const openModal = (question: Question) => {
        setSelectedQuestion(question);
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setSelectedQuestion(null);
        setIsModalOpen(false);
    };

    const confirmDelete = () => {
        if (selectedQuestion) {
            deleteQuestion(
                route('questions.destroy', {
                    question: selectedQuestion.id,
                }),
                {
                    onSuccess: () => closeModal(),
                },
            );
        }
    };

    return (
        <AppLayout title="إدارة الأسئلة" backLink={backlink}>
            <Head title="questions" />
            <div className="flex flex-col gap-4 p-4">
                {processedQuestions.map((question) => (
                    <div key={question.id} className="relative">
                        <button
                            onClick={() => openModal(question)}
                            className="absolute p-2 border-b border-r top-[10px] left-[10px] bg-secondary-200 rounded-br-xl border-secondary-300 cursor-pointer"
                        >
                            <TrashBinTrash
                                size={20}
                                weight="Linear"
                                className="text-red-500"
                            />
                        </button>
                        <Link
                            href={route('questions.edit', {
                                classroom: classroom.id,
                                question: question.id,
                            })}
                            className="flex flex-col gap-2.5 bg-secondary-200 border-secondary-600 p-2.5 rounded-md"
                        >
                            <QuestionSection
                                title="من الآية"
                                aya={question.start_verse?.text}
                            />

                            <QuestionSection
                                title="إلى الآية"
                                aya={question.end_verse?.text}
                            />

                            <div className="flex flex-col gap-1.5 px-2">
                                <hr className="border-secondary-500" />

                                <div className="flex justify-between">
                                    {/* TODO: calculate number of verses */}
                                    <h3 className="invisible font-bold text-secondary-900">
                                        12 آية
                                    </h3>
                                    <h3
                                        className="font-bold uppercase text-secondary-900"
                                        dir="ltr"
                                    >
                                        <span>#</span>
                                        <span>
                                            {question.id.split('-').at(-1)}
                                        </span>
                                    </h3>
                                </div>
                            </div>
                        </Link>
                    </div>
                ))}

                {questions.length === 0 && (
                    <div className="h-[74.5vh] rounded-xl p-1 border bg-secondary-200 border-secondary-600">
                        <div className="flex flex-col items-center justify-center h-full gap-4 p-4 text-center border sm:p-8 rounded-xl bg-secondary-100 border-secondary-300">
                            <FileText
                                size={48}
                                className="text-secondary-800 sm:size-[64px]"
                            />

                            <h2 className="text-lg font-bold sm:text-2xl text-secondary-800">
                                لا توجد بيانات
                            </h2>

                            <p className="max-w-md text-sm sm:text-base text-secondary-700">
                                لا توجد أي أسئلة حالياً. يمكنك البدء بإضافة سؤال
                                جديد لعرضه هنا واستخدامه في الامتحانات.
                            </p>

                            <Link
                                href={route('questions.create', {
                                    classroom: classroom.id,
                                })}
                                className="w-full sm:w-auto"
                            >
                                <Button className="w-full text-white sm:w-auto bg-primary-500 hover:bg-primary-600">
                                    إضافة سؤال جديد
                                </Button>
                            </Link>
                        </div>
                    </div>
                )}
            </div>

            <ConfirmationModal
                isOpen={isModalOpen}
                onClose={closeModal}
                onConfirm={confirmDelete}
                title="حذف السؤال"
                description="هل أنت متأكد من حذف السؤال؟ هذه العملية لا يمكن التراجع عنها."
            />

            {questions.length !== 0 && (
                <Link
                    href={route('questions.create', {
                        classroom: classroom.id,
                    })}
                    className="fixed p-4 text-white shadow-lg bottom-8 end-8 bg-primary-500 rounded-2xl"
                >
                    <AddCircle weight="Linear" size={24} />
                </Link>
            )}
        </AppLayout>
    );
}

export default QuestionsPage;
