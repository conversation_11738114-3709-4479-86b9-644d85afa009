import QuranViewer from '@/components/QuranViewer';
import BottomSheet from '@/components/ui/bottom-sheet';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

export default function Test() {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <AppLayout>
            <div className="w-full h-full flex items-center justify-center flex-1">
                <Button onClick={() => setIsOpen(true)}>Open Sheet</Button>
                <BottomSheet open={isOpen} setOpen={setIsOpen}>
                    <QuranViewer />
                </BottomSheet>
            </div>
        </AppLayout>
    );
}
