@props(['student', 'score', 'questions'])

@php
    $photo = $student->getFirstMedia('student_photo')
        ? $student->getFirstMedia('student_photo')->getTemporaryUrl(now()->addMinutes(10))
        : 'https://ui-avatars.com/api/?name=' . urlencode($student->name) . '&color=FFFFFF&background=C4A879&bold=true';

    $blurColor = match (true) {
        $score >= 85 => 'from-green-500 dark:from-green-700',
        $score >= 60 => 'from-yellow-400 dark:from-yellow-600',
        default => 'from-red-500 dark:from-red-800',
    };

@endphp

<div
    class="group relative flex flex-col overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-transform duration-300 hover:scale-105 dark:border-gray-700 dark:bg-gray-800">
    {{-- Gradient background blur --}}
    <div
        class="{{ $blurColor }} absolute inset-0 aspect-video -translate-y-1/2 rounded-full border bg-gradient-to-b to-white opacity-20 blur-2xl duration-300 group-hover:-translate-y-1/4">

    </div>

    {{-- Content container --}}
    <div class="relative z-10 flex h-full flex-col justify-between">
        <div class="flex flex-col items-center">
            {{-- Student Photo --}}
            <img src="{{ $photo }}" alt="{{ $student->name }}"
                class="mb-4 h-20 w-20 rounded-xl border border-[#E5D5BB] object-cover shadow-sm" />

            {{-- Student Info --}}
            <div class="mb-6 space-y-1 text-center">
                <h3 class="text-base font-bold text-gray-800 dark:text-white">{{ $student->name }}</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $student->classroom->name }}</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $student->registration_number }}</p>
            </div>

            {{-- Ratings Per Question --}}
            <ul class="mb-4 w-full space-y-1 text-sm text-gray-700 dark:text-gray-300">
                @foreach ($questions as $q)
                    <li
                        class="flex items-start justify-between gap-2 border-b border-gray-200 pb-1 dark:border-gray-700">
                        <span
                            class="max-w-[70%] whitespace-normal break-words text-sm text-gray-800 dark:text-gray-200">
                            @lang('Question'): {{ // for loop index
                                $loop->index + 1 }}
                        </span>
                        <span class="text-right text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($q->rating_summary['percentage'], 2) }}
                            <span class="text-xs text-gray-500 dark:text-gray-400">%</span>
                        </span>
                    </li>
                @endforeach
            </ul>
        </div>

        {{-- Total Score at the bottom --}}
        <div
            class="mt-auto flex flex-col items-center justify-center gap-2 pt-3 text-center text-xl font-extrabold text-gray-900 dark:text-white">
            {{ number_format($score, 2) }}%
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">
                @lang('Total Percentage')
            </p>
        </div>
    </div>
</div>
