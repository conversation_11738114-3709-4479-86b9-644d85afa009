<x-filament::page>
    @php
        $teacher = $this->record->teacher;

        $avatar =
            $teacher?->getFirstMediaUrl('profile_photo') ?:
            'https://ui-avatars.com/api/?name=' .
                urlencode($teacher?->name) .
                '&color=FFFFFF&background=c5b288&bold=true';

        $students = $this->record->students;

        $status = 'pending';
        if ($students->every(fn($s) => $s->completed_at)) {
            $status = 'completed';
        } elseif ($students->some(fn($s) => $s->started_at)) {
            $status = 'in_progress';
        }

        $statusColors = [
            'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800/20 dark:text-yellow-300',
            'in_progress' => 'bg-blue-100 text-blue-800 dark:bg-blue-800/20 dark:text-blue-300',
            'completed' => 'bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-300',
        ];

        $statusClass = $statusColors[$status];
    @endphp

    <div
        class="flex justify-between gap-4 rounded-xl border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        {{-- Exam Title & Created Date --}}
        <div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->record->name }}</h2>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                @lang('Created At'): {{ $this->record->created_at->format('F j, Y') }}
            </p>

            {{-- Exam Status --}}
            <div class="mt-4">
                <span class="{{ $statusClass }} inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold">
                    @lang('Status'): {{ ucfirst(str_replace('_', ' ', __($status))) }}
                </span>
            </div>
        </div>

        {{-- Teacher Info --}}
        <div class="flex flex-col items-center gap-4 text-center">
            <img src="{{ $avatar }}" alt="{{ $teacher?->name }}"
                class="h-15 w-15 rounded-xl border border-[#E5D5BB] object-cover shadow-sm" />
            <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $teacher?->name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">@lang('Teacher')</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4">
        @foreach ($this->record->students as $examStudent)
            @php
                $examStudent->load('questions.question');
                $questions = $examStudent->filtered_questions;
                $totalScore =
                    $questions->sum(fn($question) => $question->rating_summary['total_score']) /
                    max($questions->count(), 1);

            @endphp

            <x-exam-student-card :exam="$this->record" :student="$examStudent->student" :questions="$questions" :score="$totalScore" />
        @endforeach

    </div>
</x-filament::page>
