<div>
    <style>
        @font-face {
            font-family: 'UthmanicQaloun';
            src: url('{{ asset('fonts/UthmanicQaloun V21.ttf') }}') format('truetype');
        }

        @font-face {
            font-family: 'qaloon-v8';
            src: url('{{ asset('fonts/uthmanic_hafs_v22.ttf') }}') format('truetype');
        }

        .quran-page {
            font-family: 'UthmanicQaloun', serif;
            text-align: right;
            margin: 0;
            padding: 0;
        }

        .verse-text {
            font-size: 24px;
            padding: 4px 6px;
            color: #1e1e1e;
            transition: background-color 0.2s, box-shadow 0.2s, color 0.2s;
            cursor: pointer;
        }

        .verse-text:hover {
            background-color: rgba(220, 198, 180, 0.6);
            box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
            border-radius: 6px;
        }

        .verse-selected {
            color: #1e1915e6;
            background-color: #6e614f61;
            box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
            border-radius: 6px;
        }

        .tarqim {
            font-family: 'qaloon-v8', serif;
            font-size: 22px;
            color: #5a4e3c;
        }

        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(226, 225, 225, 1);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
            border-radius: 16px;
        }

        .chapters-select {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid #ccc;
            color: #1e1e1e;
        }

        .chapters-select:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
            border-color: rgba(59, 130, 246, 0.5);
        }

        /* === DARK MODE === */
        :root.dark .verse-text {
            color: #ddd;
        }

        :root.dark .verse-text:hover {
            background-color: rgb(58, 59, 64);
            box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);
        }

        :root.dark .verse-selected {
            color: #f0f0f0;
            background-color: rgba(100, 90, 80, 0.4);
        }

        :root.dark .tarqim {
            color: #bba;
        }

        :root.dark .glass {
            background: rgba(0, 0, 0, 0.25);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 24px rgba(255, 255, 255, 0.05);
        }

        :root.dark .chapters-select {
            background: rgba(40, 40, 40, 0.8);
            color: #eee;
            border-color: #666;
        }

        :root.dark .chapters-select:focus {
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.7);
            border-color: rgba(59, 130, 246, 0.7);
        }

        .selection-instruction {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            text-align: center;
            color: rgba(59, 130, 246, 0.8);
            font-weight: 500;
        }
    </style>

    <div class="quran-page w-full mx-auto px-4 pb-10 relative">
        <!-- Selection instruction -->
        <div class="selection-instruction">
            {{ __('Click on any verse to select it for the form field') }}
        </div>

        <div class="sticky top-20 z-20 bg-white dark:bg-neutral-900/70 backdrop-blur-sm px-4 py-3 glass flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-6 shadow-md">
            <select wire:model.live="selectedChapter" class="px-4 py-2 rounded-md w-full sm:w-auto chapters-select">
                <option value="">كل الفصول</option>
                @foreach ($chapters as $chapter)
                    <option value="{{ $chapter->id }}">{{ $chapter->name }}</option>
                @endforeach
            </select>
            <div class="flex gap-2 items-center">
                <input type="text" wire:model.live="search" class="px-4 py-2 rounded-md w-full sm:w-auto chapters-select" placeholder="ابحث عن آية">
                @if($search)
                    <p>
                        {{ $grouped->count() }} آية
                    </p>
                @endif
            </div>
        </div>

        <div class="mt-6">
            @foreach ($grouped as $key => $verses)
                @php
                    [$chapterId, $page] = explode('{-}', $key);
                    $chapterName = $verses->first()->chapter->name;
                    $juz = $verses->first()->juz;
                @endphp

                <div class="mb-8 p-6 glass shadow-md" wire:key="group-{{ $key }}">
                    {{-- Chapter Name --}}
                    @if($verses->first()->number == 1)
                        <div class="flex justify-center pb-3">
                            @php
                                $keyChapter = $chapterId.'{-}'.$page;
                            @endphp
                            @livewire('chapter-name', ['name' => $chapterName], key($keyChapter))
                        </div>
                    @endif

                    <div class="text-right text-[22px] text-gray-800 leading-relaxed pb-2" style="text-align: justify;">
                        <p class="leading-[48px]">
                            @foreach ($verses as $verse)
                                @livewire('verse', ['verse' => $verse, 'selectionMode' => true], key($verse->id))
                            @endforeach
                        </p>
                    </div>

                    <div class="flex justify-between items-center border-t border-gray-300 pt-2 text-sm text-gray-600">
                        <div>{{ $chapterName }}</div>
                        @php $keyPage = $page.'{-}'.$chapterId; @endphp
                        @livewire('page-number', ['page' => $page, 'juz' => $juz], key($keyPage))
                    </div>
                </div>
            @endforeach
        </div>

        <div x-intersect.full="$wire.loadMore()" class="p-4 text-center text-gray-600">
            <div wire:loading wire:target="loadMore">
                تحميل المزيد من الآيات...
            </div>
        </div>
    </div>
</div>
