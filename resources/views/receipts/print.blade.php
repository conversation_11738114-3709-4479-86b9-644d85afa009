<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>{{ __('Receipt') }} - {{ $receipt->receipt_number }}</title>
        <style>
            /* ===== Page / Print (DL 210×99 mm) ===== */
            @page {
                size: 210mm 99mm;
                margin: 5mm;
            }

            * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
            }

            :root {
                --fg: #000;
                --border: #000;
                --gap: 12px;
                --fs-xs: 7.5px;
                --fs-s: 9px;
                --fs-m: 10px;
                --fs-l: 12px;
                --fs-xl: 16px;
            }

            @font-face {
                font-family: 'sst-arabic';
                font-weight: 500;
                font-style: normal;
                font-display: swap;
                src: url('{{ asset('fonts/arfonts-sst-arabic-medium.ttf') }}') format('truetype');
            }

            @font-face {
                font-family: 'sst-arabic';
                font-weight: 600;
                font-style: normal;
                font-display: swap;
                src: url('{{ asset('fonts/arfonts-sst-arabic-bold.ttf') }}') format('truetype');
            }

            @font-face {
                font-family: 'sst-arabic';
                font-weight: 200;
                font-style: normal;
                font-display: swap;
                src: url('{{ asset('fonts/arfonts-sst-arabic-light.ttf') }}') format('truetype');
            }

            body {
                font-family: 'sst-arabic', sans-serif;
                font-size: var(--fs-s);
                line-height: 1.35;
                color: var(--fg);
                background: #fff;
                /* fit inside printable area (210-10 by 99-10) */
                inline-size: 200mm;
                block-size: 89mm;
                /* Better bidi defaults */
                text-align: start;
                hyphens: auto;
            }

            /* Utilities for bidi-safe alignment using logical props */
            .start {
                text-align: start;
            }

            .center {
                text-align: center;
            }

            .end {
                text-align: end;
            }

            .mi-1 {
                margin-inline: 4px;
            }

            .pi-2 {
                padding-inline: 8px;
            }

            .receipt-container {
                inline-size: 100%;
                block-size: 100%;
                display: flex;
                flex-direction: column;
                border: 2px dotted var(--border);
                padding: 10px;
            }

            /* ===== Header ===== */
            .header {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                align-items: center;
                border-block-end: 2px solid var(--border);
                padding-block-end: 8px;
                margin-block-end: 12px;
                min-block-size: 46px;
                gap: var(--gap);
            }

            .logo {
                inline-size: 36px;
                block-size: 36px;
                border: 2px solid var(--border);
                border-radius: 999px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-weight: 700;
                font-size: 15px;
                margin-inline-start: 8px;
                /* logical */
            }

            .organization-name {
                font-size: 14px;
                font-weight: 700;
            }

            .organization-subtitle {
                font-size: 8px;
            }

            .receipt-title {
                font-size: var(--fs-xl);
                font-weight: 700;
                text-transform: uppercase;
            }

            .receipt-number-header {
                font-size: var(--fs-l);
                font-weight: 700;
            }

            .receipt-date-header {
                font-size: var(--fs-m);
            }

            /* Bidi-safe inline blocks for numbers/dates to avoid flipping in RTL */
            .ltr-chunk {
                direction: ltr;
                unicode-bidi: embed;
            }

            /* ===== Content ===== */
            .main-content {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15mm;
                margin-block-end: 8px;
            }

            /* Swap columns automatically in RTL */
            [dir="rtl"] .main-content {
                direction: rtl;
            }

            [dir="rtl"] .left-column {
                order: 2;
            }

            [dir="rtl"] .right-column {
                order: 1;
            }

            .section {
                margin-block-end: 8px;
                padding-block: 4px;
            }

            .section-title {
                font-size: var(--fs-m);
                font-weight: 700;
                border-block-end: 1px solid var(--border);
                padding-block-end: 2px;
                margin-block-end: 4px;
            }

            .info-row {
                display: grid;
                grid-template-columns: auto 1fr;
                gap: 6px;
                margin-block-end: 3px;
                align-items: baseline;
            }

            .info-label {
                font-weight: 500;
            }

            .info-value {
                font-weight: 600;
                text-align: end;
            }

            [dir="rtl"] .info-value {
                text-align: start;
            }

            /* ===== Payment box ===== */
            .payment-amount {
                border: 2px solid var(--border);
                padding: 10px;
                text-align: center;
                margin-block: 10px;
                background: #fff;
            }

            .amount-label {
                font-size: var(--fs-m);
                font-weight: 700;
                margin-block-end: 4px;
            }

            .amount-value {
                font-size: var(--fs-xl);
                font-weight: 700;
                margin-block-end: 4px;
            }

            .amount-text {
                font-size: var(--fs-s);
                font-style: italic;
                margin-block-start: 2px;
            }

            /* ===== Notes ===== */
            .notes {
                border: 1px solid var(--border);
                padding: 6px;
                margin-block: 8px;
                background: #fff;
            }

            .notes-title {
                font-size: var(--fs-s);
                font-weight: 700;
                margin-block-end: 3px;
            }

            .notes-content {
                font-size: 8px;
                line-height: 1.3;
            }

            /* ===== Signatures ===== */
            .signature-area {
                display: grid;
                grid-template-columns: repeat(2, 120px);
                justify-content: space-between;
                gap: 12px;
                margin-block-start: 12px;
                padding-block-start: 10px;
                border-block-start: 1px solid var(--border);
            }

            .signature-box {
                text-align: center;
            }

            .signature-line {
                border-block-end: 1px solid var(--border);
                block-size: 22px;
                margin-block-end: 6px;
            }

            .signature-label {
                font-size: 8px;
                font-weight: 700;
            }

            /* ===== Footer ===== */
            .footer {
                margin-block-start: auto;
                padding-block-start: 8px;
                border-block-start: 1px solid var(--border);
                text-align: center;
                font-size: var(--fs-xs);
            }

            .footer-timestamp {
                font-style: italic;
            }

            /* Print */
            @media print {
                body {
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }

                .no-print {
                    display: none !important;
                }
            }

            /* Small screens preview (not print) */
            @media screen and (max-width: 480px) {
                body {
                    inline-size: 95%;
                    margin: 0 auto;
                    padding: 10px;
                }

                .main-content {
                    grid-template-columns: 1fr;
                    gap: 10px;
                }
            }
        </style>
    </head>
    <body>
        <div class="receipt-container">
            <!-- Header -->
            <div class="header">
                <!-- Start (auto flips) -->
                <div class="start">
                    <div class="receipt-number-header">
                        {{ __('Receipt Number') }}:
                        <span class="ltr-chunk">{{ $receipt->receipt_number }}</span>
                    </div>
                    <div class="receipt-date-header">
                        {{ __('Date') }}:
                        <span class="ltr-chunk">{{ $receipt->payment_date->format('Y-m-d') }}</span>
                    </div>
                </div>

                <!-- Center -->
                <div class="center">
                    <div class="receipt-title">{{ __('Payment Receipt') }}</div>
                </div>

                <!-- End (org) -->
                <div class="end" style="display:flex; align-items:center; justify-content:flex-end; gap:8px;">
                    <div class="organization-info end">
                        <div class="organization-name">مركز القلم </div>
                        <div class="organization-subtitle">لتحفيظ القرآن الكريم</div>
                    </div>
                </div>
            </div>

            <!-- Main -->
            <div class="main-content">
                <!-- Left Column -->
                <div class="left-column">
                    <div class="section">
                        <div class="section-title">{{ __('Student Information') }}</div>
                        <div class="info-row">
                            <span class="info-label">{{ __('Student Name') }}:</span>
                            <span class="info-value">{{ $receipt->student->name }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">{{ __('Registration Number') }}:</span>
                            <span class="info-value">{{ $receipt->student->registration_number ?? __('N/A') }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">{{ __('Classroom') }}:</span>
                            <span
                                class="info-value">{{ $receipt->student->classroom?->name ?? __('No Classroom') }}</span>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">{{ __('Guardian Information') }}</div>
                        <div class="info-row">
                            <span class="info-label">{{ __('Guardian Name') }}:</span>
                            <span
                                class="info-value">{{ $receipt->student->guardian?->name ?? __('No Guardian') }}</span>
                        </div>
                        @if ($receipt->student->guardian?->phone)
                            <div class="info-row">
                                <span class="info-label">{{ __('Phone') }}:</span>
                                <span class="info-value">{{ $receipt->student->guardian->phone }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Right Column -->
                <div class="right-column">
                    <div class="section">
                        <div class="section-title">{{ __('Payment Details') }}</div>
                        <div class="info-row">
                            <span class="info-label">{{ __('Payer Name') }}:</span>
                            <span class="info-value">{{ $receipt->payer_name }}</span>
                        </div>
                    </div>

                    <div class="payment-amount">
                        <div class="amount-label">{{ __('Payment Amount') }}</div>
                        <div class="amount-value ltr-chunk">
                            {{ number_format($receipt->payment_amount, 2) }} {{ __('LYD') }}
                        </div>
                        <div class="amount-text">
                            ({{ convertAmountToWords($receipt->payment_amount, app()->getLocale()) }})
                        </div>
                    </div>
                </div>
            </div>

            @if ($receipt->notes)
                <div class="notes">
                    <div class="notes-title">{{ __('Notes') }}:</div>
                    <div class="notes-content">{{ $receipt->notes }}</div>
                </div>
            @endif

            <div class="signature-area">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label">{{ __('Received by') }}</div>
                </div>
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label">{{ __('Authorized by') }}</div>
                </div>
            </div>

            <div class="footer">
                <div class="footer-contact">
                    {{ __('Contact') }}:
                    <span class="ltr-chunk">+218 92-6583858</span>
                </div>
                <div class="footer-timestamp">
                    {{ __('Generated on') }}:
                    <span class="ltr-chunk">{{ now()->format('Y-m-d H:i:s') }}</span>
                </div>
            </div>
        </div>

        <!-- Print Button -->
        <div class="no-print" style="position: fixed; inset-inline-end: 10px; inset-block-start: 10px; z-index: 1000;">
            <button onclick="window.print()"
                style="
      background:#2563eb;color:#fff;border:none;padding:10px 18px;border-radius:6px;
      cursor:pointer;font-size:14px;box-shadow:0 2px 4px rgba(0,0,0,.1);
    ">
                {{ __('Print Receipt') }}
            </button>
        </div>
    </body>
</html>
