<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Teacher;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TeacherLoginTest extends TestCase
{
    use RefreshDatabase;

    public function test_teacher_can_login(): void
    {
        $teacher = Teacher::factory()->create([
            'password' => 'password',
        ]);

        $response = $this->post(route('login'), [
            'email' => $teacher->email,
            'password' => 'password',
        ]);

        $response->assertRedirect(route('dashboard'));
        $this->assertAuthenticatedAs($teacher, 'teacher');
    }
}
