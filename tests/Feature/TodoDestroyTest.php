<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Classroom;
use App\Models\Teacher;
use App\Models\TODO;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TodoDestroyTest extends TestCase
{
    use RefreshDatabase;

    public function test_teacher_can_delete_todo_via_json(): void
    {
        $teacher = Teacher::factory()->create();
        $classroom = Classroom::factory()->create(['teacher_id' => $teacher->id]);
        $todo = TODO::create([
            'title' => 'Sample',
            'body' => 'Body',
            'classroom_id' => $classroom->id,
            'teacher_id' => $teacher->id,
        ]);

        $response = $this
            ->actingAs($teacher, 'teacher')
            ->deleteJson(route('todo.destroy', $todo->id));

        $response->assertOk();
        $this->assertSoftDeleted($todo);
    }
}
