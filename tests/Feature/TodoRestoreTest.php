<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Classroom;
use App\Models\Teacher;
use App\Models\TODO;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TodoRestoreTest extends TestCase
{
    use RefreshDatabase;

    public function test_teacher_can_restore_deleted_todo(): void
    {
        $teacher = Teacher::factory()->create();
        $classroom = Classroom::factory()->create(['teacher_id' => $teacher->id]);
        $todo = TODO::create([
            'title' => 'Sample',
            'body' => 'Body',
            'classroom_id' => $classroom->id,
            'teacher_id' => $teacher->id,
        ]);
        $todo->delete();

        $response = $this
            ->actingAs($teacher, 'teacher')
            ->post(route('todo.restore', $todo->id));

        $response->assertOk();
        $this->assertFalse($todo->fresh()->trashed());
    }
}
