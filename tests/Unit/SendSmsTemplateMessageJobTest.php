<?php

declare(strict_types=1);

use App\Enum\MessageStatus;
use App\Jobs\SendSmsTemplateMessageJob;
use App\Models\SmsMessage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

uses(RefreshDatabase::class);

it('updates sms status to sent on success', function () {
    config(['sms.api_key' => 'test', 'sms.base_url' => 'http://sms.test/api']);
    Http::fake([
        '*sms/messages/template*' => Http::response(['message_id' => '1'], 200),
    ]);

    $sms = SmsMessage::create([
        'template_id' => 'welcome',
        'sender' => 'test',
        'payment_type' => 'subscription',
        'receiver' => '1234567890',
        'params' => [],
        'status' => MessageStatus::Pending->value,
    ]);

    (new SendSmsTemplateMessageJob($sms->id))->handle();

    $sms->refresh();

    expect($sms->status)->toBe(MessageStatus::Sent->value)
        ->and($sms->message_id)->toBe('1');
});

it('marks sms as failed on error', function () {
    config(['sms.api_key' => 'test', 'sms.base_url' => 'http://sms.test/api']);
    Http::fake([
        '*sms/messages/template*' => Http::response([], 500),
    ]);

    $sms = SmsMessage::create([
        'template_id' => 'welcome',
        'sender' => 'test',
        'payment_type' => 'subscription',
        'receiver' => '1234567890',
        'params' => [],
        'status' => MessageStatus::Pending->value,
    ]);

    (new SendSmsTemplateMessageJob($sms->id))->handle();

    $sms->refresh();

    expect($sms->status)->toBe(MessageStatus::Failed->value);
});
