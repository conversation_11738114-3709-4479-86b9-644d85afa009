<?php

declare(strict_types=1);

use App\Models\Classroom;
use App\Models\Guardian;
use App\Models\Student;
use App\Models\Teacher;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

function createGuardian(): Guardian
{
    return Guardian::create([
        'name' => '<PERSON> Doe',
        'email' => '<EMAIL>',
        'phone' => '+10000000000',
        'address' => 'Address',
        'national_id' => '1234567890',
        'emergency_contact' => '+10000000001',
        'relationship_to_student' => 'Father',
    ]);
}

function createClassroom(): Classroom
{
    return Classroom::factory()->create([
        'teacher_id' => Teacher::factory()->create()->id,
    ]);
}

test('registration number generated on save', function () {
    $guardian = createGuardian();
    $classroom = createClassroom();

    $student = Student::create([
        'guardian_id' => $guardian->id,
        'classroom_id' => $classroom->id,
        'name' => 'Student Name',
        'age' => 10,
        'date_of_birth' => '2014-03-25',
        'grade_level' => 'Grade 4',
        'gender' => 'male',
        'is_active' => true,
    ]);

    expect($student->registration_number)->toBe('1'.$classroom->id.'2530001');
});

test('registration number generated on update', function () {
    $guardian = createGuardian();
    $classroom = createClassroom();

    $student = Student::withoutEvents(function () use ($guardian, $classroom) {
        return Student::create([
            'guardian_id' => $guardian->id,
            'classroom_id' => $classroom->id,
            'name' => 'Student Name',
            'age' => 10,
            'date_of_birth' => '2014-03-25',
            'grade_level' => 'Grade 4',
            'gender' => 'male',
            'is_active' => true,
        ]);
    });

    $student->update(['name' => 'Updated']);

    expect($student->fresh()->registration_number)->toBe('1'.$classroom->id.'2530001');
});
