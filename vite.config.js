import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import laravel from "laravel-vite-plugin";
import svgr from 'vite-plugin-svgr';
import { resolve } from "node:path";
import { defineConfig } from "vite";

export default defineConfig({
    plugins: [
        laravel({
            input: ["resources/css/app.css",'resources/css/filament/admin/theme.css', "resources/js/app.tsx"],
            ssr: "resources/js/ssr.tsx",
            detectTls: 'mutaqan.test',   // <— points the plugin at Herd’s cert
            refresh: true,
        }),
        react(),
        tailwindcss(),
        svgr({
            include: "**/*.svg",
            svgrOptions: {
                exportType: 'default'
            }
        })

    ],
    server: {
        host: 'mutaqan.test',
        hmr: { host: 'mutaqan.test', protocol: 'wss' }, // force wss + correct host
    },
    esbuild: {
        jsx: "automatic",
    },
    resolve: {
        alias: {
            "ziggy-js": resolve(__dirname, "vendor/tightenco/ziggy"),
        },
    },
});
